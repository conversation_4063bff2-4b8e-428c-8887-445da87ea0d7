(()=>{"use strict";var e,t,a,s,r,o,n,i={69:(e,t,a)=>{a.d(t,{BK:()=>i,eu:()=>n,q5:()=>l});var s=a(4848),r=(a(6540),a(461)),o=a(5284);function n({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function i({className:e,...t}){return(0,s.jsx)(r._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",e),...t})}function l({className:e,...t}){return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},523:(e,t,a)=>{a.d(t,{V:()=>i});var s=a(4848),r=a(5284),o=a(6948),n=a(7520);const i=()=>{const{config:e}=(0,o.UK)(),t=e?.persona||"default",a=n.z[t]||n.z.default,i=(0,r.cn)("flex","items-center","justify-center","h-full","fixed","w-full","top-[10%]","pointer-events-none"),l=(0,r.cn)("fixed","opacity-[0.03]","z-[1]");return(0,s.jsx)("div",{className:i,children:(0,s.jsx)("img",{src:a,alt:"",className:l,style:{zoom:"1.2"}})})}},1979:(e,t,a)=>{a.d(t,{z:()=>k});var s=a(4848),r=a(6540),o=a(2090),n=a(4539),i=a(6532),l=a(9696),c=a(7086),d=a(6250),u=a(37),m=a(6555),h=a(7197),p=a(5284);function g({...e}){return(0,s.jsx)(h.bL,{"data-slot":"hover-card",...e})}function f({...e}){return(0,s.jsx)(h.l9,{"data-slot":"hover-card-trigger",...e})}function x({className:e,align:t="center",sideOffset:a=4,...r}){return(0,s.jsx)(h.ZL,{"data-slot":"hover-card-portal",children:(0,s.jsx)(h.UC,{"data-slot":"hover-card-content",align:t,sideOffset:a,className:(0,p.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...r})})}var v=a(888),b=a(2955),y=a(6948),w=a(1319),j=a(1905),N=a(6174),C=a(6508);const S={...C.Af,pre:e=>(0,s.jsx)(C.AC,{...e,wrapperClassName:"my-2",className:(0,p.cn)("bg-[var(--code-bg)] text-[var(--code-text)]",e.className),buttonClassName:"h-7 w-7 text-[var(--text)] hover:bg-[var(--text)]/10"})},k=({triggerOpenCreateModal:e,onModalOpened:t})=>{const[a,h]=(0,r.useState)([]),[C,k]=(0,r.useState)(""),[$,M]=(0,r.useState)(1),[A,E]=(0,r.useState)(null),[z,_]=(0,r.useState)(!1),[T,P]=(0,r.useState)(""),[L,R]=(0,r.useState)(""),[O,D]=(0,r.useState)(""),[I,U]=(0,r.useState)(null),{config:F}=(0,y.UK)(),q=(0,r.useCallback)((async()=>{const e=await(0,b.oK)();h(e)}),[]);(0,r.useEffect)((()=>{q()}),[q]);const W=(0,r.useCallback)((e=>{E(null),P(e?.title||""),R(e?.content||""),D(""),_(!0)}),[]);(0,r.useEffect)((()=>{(async()=>{try{const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});e?.id?(console.log(`[NoteSystemView] Component mounted for tab ${e.id}. Sending SIDE_PANEL_READY signal.`),chrome.runtime.sendMessage({type:"SIDE_PANEL_READY",tabId:e.id},(e=>{chrome.runtime.lastError?console.warn("[NoteSystemView] Could not send ready signal:",chrome.runtime.lastError.message):console.log("[NoteSystemView] Background acknowledged ready signal:",e)}))):console.error("[NoteSystemView] Could not determine the tab ID to send ready signal.")}catch(e){console.error("[NoteSystemView] Error sending ready signal:",e)}})()}),[]),(0,r.useEffect)((()=>{const e=(e,t,a)=>{let s=!1;return"CREATE_NOTE_FROM_PAGE_CONTENT"===e.type&&e.payload?(console.log("[NoteSystemView] Received page data. Storing it in state to trigger auto-save."),U(e.payload),a({status:"PAGE_DATA_QUEUED_FOR_AUTO_SAVE"}),s=!0):"ERROR_OCCURRED"===e.type&&e.payload&&(console.log("[NoteSystemView] Received ERROR_OCCURRED via runtime message."),v.oR.error(String(e.payload)),a({status:"ERROR_DISPLAYED_BY_NOTESYSTEM"}),s=!0),!!s};chrome.runtime.onMessage.addListener(e);const t=chrome.runtime.connect({name:N.A.SidePanelPort});return t.postMessage({type:"init"}),t.onMessage.addListener((e=>{if("ADD_SELECTION_TO_NOTE"===e.type){console.log("[NoteSystemView] Handling ADD_SELECTION_TO_NOTE via port");const t=L?`${L}\n\n${e.payload}`:e.payload;z?R(t):W({content:t,title:"Note with Selection"}),v.oR.success("Selection added to note draft.")}})),()=>{console.log("[NoteSystemView] Cleaning up listeners."),chrome.runtime.onMessage.removeListener(e),t.disconnect()}}),[z,L,W]),(0,r.useEffect)((()=>{(async()=>{if(I){console.log("[NoteSystemView] pendingPageData detected. Attempting automatic save.");const e={...I};if(U(null),!e.content||""===e.content.trim())return void v.oR.error("Cannot save note: Content is empty.");const t={title:e.title.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:e.content,tags:[]};try{await(0,b.s2)(t),v.oR.success("Page added to notes!"),await q()}catch(e){console.error("[NoteSystemView] Error auto-saving note:",e),v.oR.error("Failed to auto-save note.")}}})()}),[I,q]),(0,r.useEffect)((()=>{e&&(W(),t())}),[e,t,W]);const B=(0,r.useMemo)((()=>{if(!C)return a;const e=C.toLowerCase();return a.filter((t=>{const a=t.title.toLowerCase().includes(e),s=t.content.toLowerCase().includes(e),r=t.tags&&t.tags.some((t=>t.toLowerCase().includes(e)));return a||s||r}))}),[a,C]),V=(0,r.useMemo)((()=>{const e=12*($-1);return B.slice(e,e+12)}),[B,$]),G=(0,r.useMemo)((()=>Math.max(1,Math.ceil(B.length/12))),[B]);return(0,s.jsxs)("div",{className:"flex flex-col h-full text-[var(--text)]",children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{type:"text",placeholder:"Search notes (titles & content & tags)...",value:C,onChange:e=>k(e.target.value),className:(0,p.cn)("w-full bg-background text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10 border-none rounded-none")}),(0,s.jsx)(d.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(n.F,{className:"flex-1",children:0===V.length?(0,s.jsx)("p",{className:"text-center text-[var(--muted-foreground)] py-4",children:C?`No notes found for "${C}".`:"No notes yet. Create one!"}):(0,s.jsx)("div",{className:"space-y-0",children:V.map((e=>(0,s.jsx)("div",{className:"px-2 border-b border-[var(--text)]/10 rounded-none hover:shadow-lg transition-shadow w-full",children:(0,s.jsxs)(g,{openDelay:200,closeDelay:100,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(f,{asChild:!0,children:(0,s.jsx)("h3",{className:"font-semibold text-md truncate cursor-pointer hover:underline",children:e.title})}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsxs)(m.AM,{children:[(0,s.jsx)(m.Wv,{asChild:!0,children:(0,s.jsx)(o.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(u.jbe,{})})}),(0,s.jsxs)(m.hl,{className:"w-30 bg-[var(--popover)] border-[var(--text)]/10 text-[var(--popover-foreground)] mr-1 p-1 space-y-1 shadow-md",children:[(0,s.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>(e=>{const t=e.tags?e.tags.join(", "):"";E(e),P(e.title),R(e.content),D(t),_(!0)})(e),children:[(0,s.jsx)(d.i5t,{className:"mr-2 size-4"}),"Edit"]}),(0,s.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>{let t="---\n";t+=`title: ${e.title}\n`;const a=e.lastUpdatedAt||e.createdAt;if(a){const e=new Date(a).toISOString().split("T")[0];t+=`date: ${e}\n`}e.tags&&e.tags.length>0&&(t+="tags:\n",e.tags.forEach((e=>{t+=`  - ${e.trim()}\n`}))),t+="---\n\n",t+=e.content;const s=document.createElement("a");s.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(t)}`),s.setAttribute("download",`${e.title}.md`),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)},children:[(0,s.jsx)(d.rII,{className:"mr-2 size-4"}),"ObsidianMD"]}),(0,s.jsxs)(o.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal text-red-500 hover:text-red-500 hover:bg-red-500/10",onClick:()=>(async e=>{await(0,b.VZ)(e),v.oR.success("Note deleted!"),q()})(e.id),children:[(0,s.jsx)(d.ttk,{className:"mr-2 size-4"})," Delete "]})]})]})})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Last updated: ",new Date(e.lastUpdatedAt).toLocaleDateString()]}),e.tags&&e.tags.length>0?(0,s.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)] truncate max-w-[50%]",children:["Tags: ",e.tags.join(", ")]}):(0,s.jsx)("p",{className:"text-xs text-[var(--muted-foreground)]",children:"No tags"})]}),(0,s.jsx)(x,{className:(0,p.cn)("bg-[var(--popover)] border-[var(--active)] text-[var(--popover-foreground)] markdown-body","w-[80vw] sm:w-[70vw] md:w-[50vw] lg:w-[40vw]","max-w-lg","max-h-[70vh]","overflow-y-auto thin-scrollbar"),side:"top",align:"start",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Date: ",new Date(e.lastUpdatedAt).toLocaleString()]}),(0,s.jsx)("div",{className:"text-sm whitespace-pre-wrap break-words",children:(0,s.jsx)(w.oz,{remarkPlugins:[j.A],components:S,children:e.content})}),e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"border-t border-[var(--border)] pt-2 mt-2",children:[(0,s.jsx)("p",{className:"text-xs font-semibold text-[var(--text)] mb-1",children:"Tags:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e=>(0,s.jsx)("span",{className:"text-xs bg-[var(--muted)] text-[var(--muted-foreground)] px-2 py-0.5 rounded",children:e},e)))})]})]})})]})},e.id)))})}),G>1&&(0,s.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 font-['Space_Mono',_monospace]",children:[(0,s.jsx)(o.$,{onClick:()=>M((e=>Math.max(1,e-1))),disabled:1===$,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,s.jsxs)("span",{className:"text-md",children:["Page ",$," of ",G]}),(0,s.jsx)(o.$,{onClick:()=>M((e=>Math.min(G,e+1))),disabled:$===G,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]}),(0,s.jsx)(c.lG,{open:z,onOpenChange:e=>{e?_(!0):(_(!1),E(null),P(""),R(""),D(""))},children:(0,s.jsxs)(c.Cf,{className:(0,p.cn)("bg-[var(--bg)] border-[var(--text)]/10 w-[90vw] max-w-3xl text-[var(--text)] overflow-hidden","flex flex-col max-h-[85vh]","p-6"),children:[(0,s.jsxs)(c.c7,{children:[(0,s.jsx)(c.L3,{children:A?"Edit Note":"Create New Note"}),(0,s.jsx)(c.rr,{className:"text-[var(--text)]/80 pt-1",children:A?"Update the title or content of your note.":"Provide a title (optional) and content for your new note."})]}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 space-y-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)(i.p,{placeholder:"Note Title (optional)",value:T,onChange:e=>P(e.target.value),className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto thin-scrollbar min-h-0",children:(0,s.jsx)(l.T,{placeholder:"Your note content...",value:L,onChange:e=>R(e.target.value),autosize:!0,minRows:5,className:"w-full bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] resize-none overflow-hidden"})}),(0,s.jsx)("div",{children:(0,s.jsx)(i.p,{placeholder:"Tags (comma-separated)",value:O,onChange:e=>{D(e.target.value)},className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})})]}),(0,s.jsxs)(c.Es,{children:[(0,s.jsx)(o.$,{variant:"outline",onClick:()=>{_(!1),E(null)},children:"Cancel"}),(0,s.jsx)(o.$,{onClick:async()=>{if(!L.trim())return void v.oR.error("Note content cannot be empty.");const e=""===O.trim()?[]:O.split(",").map((e=>e.trim())).filter((e=>e.length>0)),t={id:A?.id,title:T.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:L,tags:e};await(0,b.s2)(t),v.oR.success(A?"Note updated!":"Note created!"),await q(),_(!1),E(null),P(""),R(""),D("")},className:"bg-[var(--active)] text-[var(--active-foreground)] hover:bg-[var(--active)]/90",children:A?"Save Changes":"Create Note"})]})]})})]})}},2090:(e,t,a)=>{a.d(t,{$:()=>l});var s=a(4848),r=(a(6540),a(3362)),o=a(2732),n=a(5284);const i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none not-focus-visible",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 not-focus-visible",outline:"border bg-background shadow-xs hover:bg-accent",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:text-foreground hover:bg-black/10 dark:hover:bg-white/10",link:"text-primary underline-offset-4 hover:underline","message-action":"bg-transparent text-muted-foreground p-0 shadow-none hover:bg-transparent focus:bg-transparent active:text-muted active:[&_svg]:text-muted-foreground transition-colors duration-75","ghost-themed":"text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground","outline-themed":"border border-[var(--active)] bg-transparent text-[var(--active)] shadow-xs hover:bg-[var(--active)]/20 focus-visible:bg-[var(--active)]/20 focus-visible:ring-1 focus-visible:ring-[var(--active)]","destructive-outline":"border border-destructive bg-transparent text-destructive shadow-xs hover:bg-destructive/10 hover:text-destructive-foreground focus-visible:bg-destructive/10 focus-visible:text-destructive-foreground focus-visible:ring-1 focus-visible:ring-destructive","copy-button":"bg-background text-foreground shadow-none hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground",connect:"bg-[var(--input-background)] text-[var(--text)] hover:bg-[var(--active)]/90 shadow-sm focus-visible:ring-1 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]","active-bordered":"bg-[var(--active)] text-[var(--text)] border border-[var(--text)] hover:brightness-110 focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm","outline-subtle":"border border-[var(--text)]/50 bg-transparent text-[var(--text)] hover:bg-[var(--text)]/10 hover:border-[var(--text)]/70 hover:text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm"},size:{default:"h-9 px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",sm:"h-8 rounded-md px-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-4",lg:"h-10 rounded-md px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",icon:"size-8 [&_svg:not([class*='size-'])]:size-7",xs:"h-6 w-6 p-0 rounded-sm [&_svg:not([class*='size-'])]:size-3.5 text-xs"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:o=!1,...l}){const c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:a,className:e})),...l})}},2951:(e,t,a)=>{a.d(t,{hL:()=>i,GW:()=>s,hj:()=>l,tE:()=>n});const s=async(e,t,a,s,r,o=[],n)=>{try{if(!a?.host)return console.error("processQueryWithAI: currentModel or currentModel.host is undefined. Cannot determine API URL."),e;const i=o.map((e=>`{{${e.role}}}: ${e.content}`)).join("\n"),l=`You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${i}].\n\n\nInstructions:\n**Important** No Explanation, just the optimized query!\n\n\n1. Extract the key keywords and named entities from the user's input.\n2. Correct any obvious spelling errors.\n3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.\n4. If the input is nonsensical or not a query, return the original input.\n5. Using previous chat history to understand the user's intent.\n\n\nOutput:\n'The optimized Google search query'\n\n\nExample 1:\nInput from user ({{user}}): where can i find cheep flights to london\nOutput:\n'cheap flights London'\n\n\nExample 2:\nContext: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!\nInput from user ({{user}}): please choose me the best restarant\nOutput:\n'best restaurants Paris France'\n\n\nExample 3:\nInput from user ({{user}}): asdf;lkjasdf\nOutput:\n'asdf;lkjasdf'\n`,c={ollama:`${t?.ollamaUrl||""}/api/chat`}[a.host];if(!c)return console.error("processQueryWithAI: Could not determine API URL for host:",a.host),e;console.log(`processQueryWithAI: Using API URL: ${c} for host: ${a.host}`),console.log("Formatted Context for Prompt:",i);const d={model:t?.selectedModel||a.id||"",messages:[{role:"system",content:l},{role:"user",content:e}],stream:!1};let u;void 0!==n?u=n:void 0!==t.temperature&&(u=t.temperature),void 0!==u&&(d.temperature=u);const m=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json",...s||{}},signal:r,body:JSON.stringify(d)});if(!m.ok){const e=await m.text();throw console.error(`API request failed with status ${m.status}: ${e}`),new Error(`API request failed: ${m.statusText}`)}const h=await m.json(),p=h?.choices?.[0]?.message?.content;return"string"==typeof p?(e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["']/g,"").trim())(p):e}catch(t){if(r?.aborted||t instanceof Error&&"AbortError"===t.name)throw console.log("processQueryWithAI: Operation aborted."),t;return console.error("processQueryWithAI: Error during execution:",t),e}},r=async function(e){try{const t=new URL(e);if("chrome:"===t.protocol)return;const a=[{id:1,priority:1,condition:{requestDomains:[t.hostname]},action:{type:"modifyHeaders",requestHeaders:[{header:"Origin",operation:"set",value:`${t.protocol}//${t.hostname}`}]}}];await chrome.declarativeNetRequest.updateDynamicRules({removeRuleIds:a.map((e=>e.id)),addRules:a})}catch(e){console.debug("URL rewrite skipped:",e)}},o=e=>{try{const t=(new DOMParser).parseFromString(e,"text/html");t.querySelectorAll('script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]').forEach((e=>e.remove()));let a=t.querySelector("main")||t.querySelector("article")||t.querySelector(".content")||t.querySelector("#content")||t.querySelector(".main-content")||t.querySelector("#main-content")||t.querySelector(".post-content")||t.body,s=a?.textContent||"";return s=s.replace(/\s+/g," ").trim(),s=s.split("\n").filter((e=>e.trim().length>20)).join("\n"),s}catch(e){return console.error("Error parsing HTML for content extraction:",e),"[Error extracting content]"}},n=async(e,t,a)=>{console.log("[webSearch] Received query:",e),console.log("[webSearch] Web Mode from config:",t?.webMode);const s=t.webMode,r=t.serpMaxLinksToVisit??3,n=t.webLimit&&128!==t.webLimit?1e3*t.webLimit:1/0,i=a||(new AbortController).signal;if(console.log(`Performing ${s} search for: "${e}"`),"Google"===s&&console.log(`[webSearch - ${s}] Max links to visit for content scraping: ${r}`),!s)return console.error("[webSearch] Web search mode is undefined. Aborting search. Config was:",JSON.stringify(t)),"Error: Web search mode is undefined. Please check your configuration.";try{if("Google"===s){const t=new AbortController,a=setTimeout((()=>{console.warn(`[webSearch - ${s}] SERP API call timed out after 15s.`),t.abort()}),15e3),l="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i,c=`https://www.google.com/search?q=${encodeURIComponent(e)}&hl=en&gl=us`,d=await fetch(c,{signal:l,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Language":"en-US,en;q=0.9","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","upgrade-insecure-requests":"1",Referer:"https://www.google.com/"}}).finally((()=>{clearTimeout(a)}));if(!d.ok)throw new Error(`Web search failed (${s}) with status: ${d.status}`);if(i.aborted)throw new Error("Web search operation aborted.");const u=await d.text(),m=new DOMParser;console.log(`[webSearch - ${s}] SERP HTML (first 500 chars):`,u.substring(0,500));const h=m.parseFromString(u,"text/html"),p=[];if(h.querySelectorAll("div.g, div.MjjYud, div.hlcw0c").forEach((e=>{const t=e.querySelector("a[href]"),a=t?.getAttribute("href"),s=e.querySelector("h3"),r=s?.textContent?.trim()||"";let o="";const n=e.querySelectorAll('div[style="-webkit-line-clamp:2"], div[data-sncf="1"], .VwiC3b span, .MUxGbd span');if(n.length>0)o=Array.from(n).map((e=>e.textContent)).join(" ").replace(/\s+/g," ").trim();else{const t=e.textContent||"",a=r?t.indexOf(r):-1;-1!==a&&(o=t.substring(a+r.length).replace(/\s+/g," ").trim().substring(0,300))}r&&a&&a.startsWith("http")&&p.push({title:r,snippet:o,url:a})})),console.log(`[webSearch - ${s}] Parsed SERP Results (${p.length} found, showing first 5):`,JSON.stringify(p.slice(0,5))),0===p.length)return console.log("No search results found on SERP."),"No results found.";const g=p.slice(0,r).filter((e=>e.url));console.log(`Found ${p.length} results. Attempting to fetch content from top ${g.length} links (maxLinksToVisit: ${r}).`);const f=g.map((async e=>{if(!e.url)return{...e,content:"[Invalid URL]",status:"error"};if(i.aborted)return{...e,content:`[Fetching aborted by user: ${e.url}]`,status:"aborted"};console.log(`Fetching content from: ${e.url}`);const t=new AbortController,a=setTimeout((()=>{console.warn(`[webSearch] Page scrape for ${e.url} timed out after 12s.`),t.abort()}),12e3),r="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i;let n=`[Error fetching/processing: Unknown error for ${e.url}]`,l="error";try{const t=await fetch(e.url,{signal:r,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9"}});if(!t.ok)throw new Error(`Failed to fetch ${e.url} - Status: ${t.status}`);const a=t.headers.get("content-type");if(!a||!a.includes("text/html"))throw new Error(`Skipping non-HTML content (${a}) from ${e.url}`);if(i.aborted)throw new Error("Web search operation aborted by user.");const c=await t.text();n=o(c),l="success",console.log(`[webSearch - ${s}] Successfully fetched and extracted content from: ${e.url} (Extracted Length: ${n.length})`)}catch(a){if("AbortError"===a.name){if(i.aborted)throw a;n=t.signal.aborted?`[Timeout fetching: ${e.url}]`:`[Fetching aborted: ${e.url}]`,l="aborted"}else n=`[Error fetching/processing: ${a.message}]`,l="error"}finally{clearTimeout(a)}return{...e,content:n,status:l}})),x=await Promise.allSettled(f);if(i.aborted)throw new Error("Web search operation aborted.");let v=`Search results for "${e}" using ${s}:\n\n`,b=0;return p.forEach(((e,t)=>{if(v+=`[Result ${t+1}: ${e.title}]\n`,v+=`URL: ${e.url||"[No URL Found]"}\n`,v+=`Snippet: ${e.snippet||"[No Snippet]"}\n`,t<g.length){const t=x[b];if("fulfilled"===t?.status){const a=t.value;if(a.url===e.url){const e=a.content.substring(0,n);v+=`Content:\n${e}${a.content.length>n?"...":""}\n\n`}else v+=`Content: [Content fetch mismatch - data for ${a.url} found, expected ${e.url}]\n\n`}else v+="rejected"===t?.status?`Content: [Error fetching: ${t.reason}]\n\n`:"Content: [Fetch status unknown]\n\n";b++}else v+="Content: [Not fetched due to link limit]\n\n"})),console.log("Web search finished. Returning combined results."),v.trim()}return`Unsupported web search mode: ${s}`}catch(e){if("AbortError"===e.name&&i.aborted)throw console.log("[webSearch] Operation aborted by signal."),e;return console.error("Web search overall failed:",e),`Error performing web search: ${e.message}`}};async function i(e,t,a,s={},o,n){let i=!1;const l=(e,t=!1)=>{if(!i){let s;i=!0,s="string"==typeof e?e:e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:String(e),a(s,!0,t)}},c=()=>{if(n?.aborted)throw new Error("Streaming operation aborted by user.")};if(e.startsWith("chrome://"))console.log("fetchDataAsStream: Skipping chrome:// URL:",e);else{e.includes("localhost")&&await r((e=>e.endsWith("/")?e.slice(0,-1):e)(e));try{const r=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",...s},body:JSON.stringify(t),signal:n});if(!r.ok){let e=`Network response was not ok (${r.status})`;try{e+=`: ${await r.text()||r.statusText}`}catch(t){e+=`: ${r.statusText}`}throw new Error(e)}let d="";if("ollama"!==o)throw new Error(`Unsupported host specified: ${o}`);{if(!r.body)throw new Error("Response body is null for Ollama");const e=r.body.getReader();let t,s;for(;c(),({value:s,done:t}=await e.read()),!t;){const t=(new TextDecoder).decode(s).split("\n").filter((e=>""!==e.trim()));for(const s of t){if("[DONE]"===s.trim())return n?.aborted&&e.cancel(),void l(d);try{const t=JSON.parse(s);if(t.message?.content&&(d+=t.message.content,i||a(d)),!0===t.done&&!i)return n?.aborted&&e.cancel(),void l(d)}catch(e){console.debug("Skipping invalid JSON chunk:",s)}}}n?.aborted&&e.cancel(),l(d)}}catch(e){n?.aborted?(console.log("[fetchDataAsStream] Operation aborted via signal as expected. Details:",e),l("",!1)):e instanceof Error&&"AbortError"===e.name?(console.log("[fetchDataAsStream] AbortError (name check) caught. Operation was cancelled. Details:",e),l("",!1)):(console.error("Error in fetchDataAsStream (unexpected):",e),l(e instanceof Error?e.message:String(e),!0))}}}async function l(e,t){const a=new AbortController,s=t||a.signal,r=t?null:setTimeout((()=>a.abort()),12e3);try{const t=await fetch(e,{signal:s,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9"}});if(r&&clearTimeout(r),s.aborted)throw new Error("Scraping aborted by user.");if(!t.ok)throw new Error(`Failed to fetch ${e} - Status: ${t.status}`);const a=t.headers.get("content-type");if(!a||!a.includes("text/html"))throw new Error(`Skipping non-HTML content (${a}) from ${e}`);const n=await t.text();return o(n)}catch(t){return r&&clearTimeout(r),"AbortError"===t.name?`[Scraping URL aborted: ${e}]`:`[Error scraping URL: ${e} - ${t.message}]`}}},2955:(e,t,a)=>{a.d(t,{VZ:()=>l,oK:()=>i,s2:()=>n});var s=a(3790),r=a.n(s);const o="cognito_note_",n=async e=>{const t=Date.now(),a=e.id||`${o}${Date.now()}_${Math.random().toString(16).slice(2)}`,s=e.id?await r().getItem(a):null,n={id:a,title:e.title||`Note - ${new Date(t).toLocaleDateString([],{year:"numeric",month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit"})}`,content:e.content,createdAt:s?.createdAt||t,lastUpdatedAt:t,tags:e.tags};return await r().setItem(a,n),n},i=async()=>{const e=(await r().keys()).filter((e=>e.startsWith(o))),t=[];for(const a of e){const e=await r().getItem(a);e&&t.push(e)}return t.sort(((e,t)=>t.lastUpdatedAt-e.lastUpdatedAt))},l=async e=>{await r().removeItem(e),console.log("Note deleted from system:",e)}},3003:(e,t,a)=>{a.a(e,(async(e,t)=>{try{var s=a(4848),r=a(5338),o=a(1468),n=a(3190),i=a(6174),l=a(9828),c=a(6948),d=e([l]);l=(d.then?(await d)():d)[0];const u=(0,n.g)(i.A.ContentPort),m=document.getElementById("root");u.ready().then((()=>{if(null==m)throw new Error("Root container not found");(0,r.createRoot)(m).render((0,s.jsx)(o.Kq,{store:u,children:(0,s.jsx)(c.sG,{children:(0,s.jsx)(l.A,{})})}))})),t()}catch(e){t(e)}}))},3108:(e,t,a)=>{a.d(t,{Y:()=>O});var s=a(4848),r=a(6540),o=a(3),n=a(5066),i=a(6948),l=a(5284),c=a(888),d=a(2090),u=a(990),m=a(8697);function h({...e}){return(0,s.jsx)(u.bL,{"data-slot":"sheet",...e})}function p({...e}){return(0,s.jsx)(u.ZL,{"data-slot":"sheet-portal",...e})}function g({className:e,...t}){return(0,s.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}const f=r.forwardRef((({className:e,children:t,side:a="right",variant:r="default",...o},n)=>{const i="themedPanel"===r?((e="right")=>(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full border-l","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full border-r","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t","bg-[var(--bg)] text-[var(--text)] shadow-xl"))(a):((e="right")=>(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t"))(a);return(0,s.jsxs)(p,{children:[(0,s.jsx)(g,{})," ",(0,s.jsxs)(u.UC,{ref:n,"data-slot":"sheet-content",className:(0,l.cn)(i,e),...o,children:[t,(0,s.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(m.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}));function x({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",e),...t})}function v({className:e,...t}){return(0,s.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",e),...t})}function b({className:e,...t}){return(0,s.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}f.displayName=u.UC.displayName;var y=a(9018),w=a(6532),j=a(3885),N=a(69),C=a(8698),S=a(7520);const k=()=>{const{config:e}=(0,i.UK)(),t=(0,r.useRef)(null);return(0,r.useEffect)((()=>{if(!e?.animatedBackground)return;const a=t.current;if(a){a.innerHTML="";{const s=document.createElement("canvas");a.appendChild(s);const r=s.getContext("2d");if(!r)return;const o=16,n=1.2*o,i=1.2*o;function l(){s.width=window.innerWidth,s.height=window.innerHeight}l(),window.addEventListener("resize",l);const c=["N","ﾐ","ﾋ","𐌊","ｳ","ｼ","ﾅ","𐌔","X","ｻ","ﾜ","ㄘ","𑖖","𑖃","𑖆","𐌈","J","ｱ","ﾎ","ﾃ","M","π","Σ","Y","ｷ","ㄠ","ﾕ","ﾗ","ｾ","ﾈ","Ω","ﾀ","ﾇ","ﾍ","ｦ","ｲ","ｸ","W","𐌙","ﾁ","ﾄ","ﾉ","Δ","ﾔ","ㄖ","ﾙ","ﾚ","王","道","Ж","ﾝ","0","1","2","3","4","5","7","8","9","A","B","Z","*","+","д","Ⱟ","𑗁","T","|","ç","ﾘ","Ѯ"],d=["#15803d","#16a34a","#22c55e","#4ade80"],u="#f0fdf4";let m=Math.floor(s.width/n),h=Math.floor(s.height/i),p=Array(m).fill(0),g=Array(m).fill(null).map((()=>Array(h).fill({char:"",color:""}))),f=Array(m).fill(0).map((()=>Math.floor(2*Math.random())+1)),x=Array(m).fill(0);const v=12;function b(){r.clearRect(0,0,s.width,s.height),r.font=`${o}px monospace`,r.textAlign="center",r.textBaseline="top";for(let e=0;e<m;e++){for(let t=0;t<v;t++){const a=p[e]-t;if(a<0)continue;if(a>=h)continue;let s=g[e][a];s&&s.char&&(r.fillStyle=0===t?u:s.color,r.globalAlpha=.3*(1-t/v),r.fillText(s.char,e*n+n/2,a*i))}if(r.globalAlpha=1,x[e]++,x[e]>=f[e]){const t=c[Math.floor(Math.random()*c.length)],a=d[Math.floor(Math.random()*d.length)];g[e][p[e]]={char:t,color:a},p[e]++,p[e]>=h+v&&(p[e]=0,g[e]=Array(h).fill({char:"",color:""}),f[e]=Math.floor(10*Math.random())+10),x[e]=0}}requestAnimationFrame(b)}b();const y=()=>{l(),m=Math.floor(s.width/n),h=Math.floor(s.height/i),p=Array(m).fill(0)};return window.addEventListener("resize",y),()=>{window.removeEventListener("resize",l),window.removeEventListener("resize",y),a.removeChild(s)}}}}),[e?.animatedBackground]),e?.animatedBackground?(0,s.jsx)("div",{ref:t,style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:-1,pointerEvents:"none",overflow:"hidden"}}):null},$=({isOpen:e,onOpenChange:t,config:a,updateConfig:n,setSettingsMode:i,setHistoryMode:c,setNoteSystemMode:u})=>{const[m,p]=r.useState(""),[$,M]=r.useState(!1),{fetchAllModels:A}=(0,C.N)(),E=r.useRef(null),z=(0,r.useRef)(null),[_,T]=r.useState({top:0,left:0,width:0}),P=a?.persona||"default",L=S.z[P]||S.z.default,R=a?.models?.filter((e=>e.id.toLowerCase().includes(m.toLowerCase())||e.host?.toLowerCase()?.includes(m.toLowerCase())))||[];return(0,r.useEffect)((()=>{e&&(p(""),M(!1))}),[e]),(0,r.useEffect)((()=>{if($&&z.current){const e=z.current.getBoundingClientRect();T({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}}),[$]),(0,r.useEffect)((()=>{if(!$)return;const e=()=>{if(z.current){const e=z.current.getBoundingClientRect();T({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}};return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}),[$]),(0,s.jsxs)(h,{open:e,onOpenChange:t,children:[(0,s.jsx)(g,{}),(0,s.jsxs)(f,{variant:"themedPanel",side:"left",className:(0,l.cn)("p-0 border-r-0","w-[22.857rem] sm:w-[27.143rem]","flex flex-col h-full max-h-screen","[&>button]:hidden","settings-drawer-content","overflow-y-auto"),style:{height:"100dvh"},ref:E,onOpenAutoFocus:e=>{e.preventDefault(),E.current?.focus({preventScroll:!0})},children:[(0,s.jsx)(k,{}),(0,s.jsx)("div",{className:(0,l.cn)("border border-[var(--active)]","sticky top-0 z-10 p-0")}),(0,s.jsxs)(x,{className:"px-4 pt-4 pb-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-2 relative z-10",children:(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{variant:"ghost",size:"sm","aria-label":"Close Settings",className:"text-[var(--text)] rounded-md relative top-[1px]",onClick:()=>t(!1),children:(0,s.jsx)(o.yGN,{size:"20px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:" Close Settings "})]})}),(0,s.jsx)(v,{className:"text-center font-['Bruno_Ace_SC'] tracking-tight -mt-10 cognito-title-container",children:(0,s.jsxs)("a",{href:"https://github.com/3-ark/Cognito",target:"_blank",rel:"noopener noreferrer",className:(0,l.cn)("text-xl font-semibold text-[var(--text)] bg-[var(--active)] inline-block px-3 py-1 rounded-md no-underline","chromepanion-title-blade-glow"),children:["CHROMEPANION ",(0,s.jsxs)("sub",{className:"contrast-200 text-[0.5em]",children:["v","3.7.4"]})]})}),(0,s.jsx)(b,{className:"text-center font-['Bruno_Ace_SC'] text-[var(--text)] leading-tight mt-2",children:"Settings"})]}),(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-col h-full overflow-y-auto settings-drawer-body","no-scrollbar"),children:[(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-col space-y-5 flex-1","px-6","py-4"),children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex items-center justify-between mt-5 mb-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("label",{htmlFor:"persona-select",className:"text-[var(--text)] opacity-80 font-['Bruno_Ace_SC'] text-lg shrink-0",children:"Persona"}),(0,s.jsxs)(N.eu,{className:"h-8 w-8 border border-[var(--active)]",children:[(0,s.jsx)(N.BK,{src:L,alt:P}),(0,s.jsx)(N.q5,{children:P.substring(0,1).toUpperCase()})]})]})}),(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)(y.l6,{value:P,onValueChange:e=>n({persona:e}),children:[(0,s.jsx)(y.bq,{id:"persona-select",variant:"settingsPanel",className:"w-full font-['Space_Mono',_monospace] data-[placeholder]:text-muted-foreground",children:(0,s.jsx)(y.yv,{placeholder:"Select Persona..."})}),(0,s.jsx)(y.gC,{variant:"settingsPanel",children:Object.keys(a?.personas||{}).map((e=>(0,s.jsx)(y.eb,{value:e,className:(0,l.cn)("hover:brightness-95 focus:bg-[var(--active)]","font-['Space_Mono',_monospace]"),children:e},e)))})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"model-input",className:"block text-[var(--text)] opacity-80 text-lg font-['Bruno_Ace_SC']",children:"Model"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(w.p,{id:"model-input",ref:z,value:$?m:a?.selectedModel||"",placeholder:$?"Search models...":a?.selectedModel||"Select model...",onChange:e=>p(e.target.value),onFocus:()=>{p(""),M(!0),A()},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9 font-['Space_Mono',_monospace]","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-95","mb-2 mt-3","ring-1 ring-inset ring-[var(--active)]/50")}),$&&(0,s.jsx)("div",{className:"fixed inset-0 z-50",onClick:()=>M(!1),children:(0,s.jsx)("div",{className:(0,l.cn)("absolute left-0 right-0","bg-[var(--bg)]","border border-[var(--active)]/20","rounded-xl shadow-lg","no-scrollbar","overflow-y-auto"),style:{maxHeight:"min(calc(50vh - 6rem), 300px)",top:`${_.top}px`,left:`${_.left}px`,width:`${_.width}px`},onClick:e=>e.stopPropagation(),children:(0,s.jsx)("div",{className:"py-0.5",children:R.length>0?R.map((e=>(0,s.jsx)("button",{type:"button",className:(0,l.cn)("w-full text-left","px-4 py-1.5","text-[var(--text)] text-sm","hover:bg-[var(--active)]/20","focus:bg-[var(--active)]/30","transition-colors duration-150","font-['Space_Mono',_monospace]"),onClick:()=>{n({selectedModel:e.id}),p(""),M(!1)},children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("span",{children:[e.host?`(${e.host}) `:"",e.id,e.context_length&&(0,s.jsxs)("span",{className:"text-xs text-[var(--text)] opacity-50 ml-1",children:["[ctx: ",e.context_length,"]"]})]})})},e.id))):(0,s.jsx)("div",{className:"px-4 py-1.5 text-[var(--text)] opacity-50 text-sm",children:"No models found"})})})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(d.$,{size:"default",onClick:()=>{i(!0),t(!1)},variant:"outline",className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4"),children:"Configuration"}),(0,s.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{c(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Chat History"}),(0,s.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{u(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Note System"})]})]}),(0,s.jsx)("div",{className:(0,l.cn)("mt-auto text-center text-[var(--text)] opacity-70 shrink-0 text-xs font-mono pb-4"),children:"Made with ❤️ by @3-Arc"})]})]})]})};var M=a(7086),A=a(5634),E=a(3720),z=a(3732),_=a(6250),T=a(6973);const P=({isOpen:e,onClose:t,setSettingsMode:a})=>(0,s.jsx)(M.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(M.Cf,{variant:"themedPanel",className:(0,l.cn)("[&>button]:hidden","bg-card border border-border shadow-lg"),style:{width:"20rem",height:"12rem",borderRadius:"var(--radius-lg)",boxShadow:"var(--shadow-lg)"},onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(M.c7,{className:"text-center p-4",children:(0,s.jsx)(M.L3,{className:"text-apple-title3 text-foreground",children:"Welcome to Chromepanion"})}),(0,s.jsx)(M.rr,{asChild:!0,children:(0,s.jsxs)("div",{className:"px-6 pb-6 text-center",children:[(0,s.jsx)("p",{className:"text-apple-body text-muted-foreground mb-6",children:"Get started by connecting to your AI models"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(d.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>a(!0),"aria-label":"Open Settings",children:"Open Settings"})})]})})]})}),L=({children:e})=>(0,s.jsx)("div",{className:(0,l.cn)("inline-block whitespace-nowrap overflow-hidden text-ellipsis w-full max-w-xs","bg-transparent text-[var(--text)]","rounded-md py-0.5","font-['poppins',_sans-serif] text-md text-center font-medium"),children:e}),R=({isOpen:e,onOpenChange:t,config:a,updateConfig:o})=>{const[n,i]=(0,r.useState)(a?.userName||""),[u,m]=(0,r.useState)(a?.userProfile||"");return(0,r.useEffect)((()=>{e&&(i(a?.userName||""),m(a?.userProfile||""))}),[e,a?.userName,a?.userProfile]),(0,s.jsx)(M.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(M.Cf,{variant:"themedPanel",className:"max-w-xs",children:[(0,s.jsxs)(M.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,s.jsx)(M.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Edit Profile"}),(0,s.jsx)(M.rr,{className:"text-sm text-[var(--text)] opacity-80",children:"Set your display name and profile information. (For chat and export purposes)"})]}),(0,s.jsxs)("div",{className:"px-6 py-5 space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsx)(A.J,{htmlFor:"username",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"Username"}),(0,s.jsx)(w.p,{id:"username",value:n,onChange:e=>i(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsx)(A.J,{htmlFor:"userprofile",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"User Profile"}),(0,s.jsx)(w.p,{id:"userprofile",value:u,onChange:e=>m(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]})]}),(0,s.jsxs)(M.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,s.jsx)(d.$,{variant:"outline-subtle",size:"sm",onClick:()=>t(!1),children:"Cancel"}),(0,s.jsx)(d.$,{variant:"active-bordered",size:"sm",onClick:()=>{o({userName:n,userProfile:u}),t(!1),c.oR.success("Profile updated!")},children:"Save Changes"})]})]})})},O=({chatTitle:e,settingsMode:t,setSettingsMode:a,historyMode:u,setHistoryMode:m,noteSystemMode:h,setNoteSystemMode:p,deleteAll:g,reset:f,downloadImage:x,downloadJson:v,downloadText:b,downloadMarkdown:y,chatMode:w,chatStatus:N,onAddNewNoteRequest:C})=>{const{config:k,updateConfig:M}=(0,i.UK)(),[A,O]=(0,r.useState)(!1),D=k?.persona||"default",I=(S.z[D]||S.z.default,e&&!t&&!u&&!h),[U,F]=(0,r.useState)(!1),q=t||u||h,W=q?"Back to Chat":"",B="z-50 min-w-[6rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",V="flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none transition-colors focus:bg-accent focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50";return(0,s.jsx)(j.Bc,{delayDuration:500,children:(0,s.jsxs)("div",{className:(0,l.cn)("bg-background/95 backdrop-blur-sm text-foreground","border-b border-border","sticky top-0 z-10"),children:[(0,s.jsxs)("div",{className:"flex items-center h-auto py-3 px-4",children:[(0,s.jsx)("div",{className:"flex justify-start items-center min-h-10 w-12",children:q&&(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":W,variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{q&&(a(!1),m(!1),p(!1))},children:(0,s.jsx)(o.yGN,{size:"18px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:W})]})}),(0,s.jsxs)("div",{className:"flex-grow flex justify-center items-center overflow-hidden px-4",children:[I&&(0,s.jsx)("p",{className:"text-apple-headline text-foreground whitespace-nowrap overflow-hidden text-ellipsis text-center",children:e}),!I&&!u&&!t&&!h&&(0,s.jsx)(L,{className:"bg-secondary text-secondary-foreground border-border",children:k?.selectedModel||"No Model Selected"}),t&&(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Settings"})}),u&&(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Chat History"})}),h&&(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Note System"})})]}),(0,s.jsxs)("div",{className:"flex justify-end items-center min-h-10 gap-2",children:[!t&&!u&&!h&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Reset Chat",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center group",onClick:f,children:(0,s.jsx)(n.yPB,{size:"16px",className:"transition-transform duration-300 rotate-0 group-hover:rotate-180"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Reset Chat"})]}),(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Settings",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{a(!0)},children:(0,s.jsx)(o.VSk,{size:"16px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Settings"})]}),(0,s.jsxs)(E.bL,{children:[(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(E.l9,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Share Options",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",children:(0,s.jsx)(o.pdY,{size:"18px"})})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Share Options"})]}),(0,s.jsx)(E.ZL,{children:(0,s.jsxs)(E.UC,{className:(0,l.cn)(B,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-xl"),sideOffset:5,align:"end",children:[(0,s.jsxs)(E.q7,{className:(0,l.cn)(V,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:()=>O(!0),children:[(0,s.jsx)(z.uSr,{className:"mr-auto h-4 w-4"}),"Edit Profile"]}),(0,s.jsx)(E.wv,{className:(0,l.cn)("-mx-1 my-1 h-px bg-muted","bg-[var(--text)]/10")}),(0,s.jsxs)(E.Pb,{children:[(0,s.jsxs)(E.ZP,{className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent","hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),children:[(0,s.jsx)(o.irw,{className:"mr-auto h-4 w-4"}),"Export Chat"]}),(0,s.jsx)(E.ZL,{children:(0,s.jsxs)(E.G5,{className:(0,l.cn)(B,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-lg"),sideOffset:2,alignOffset:-5,children:[(0,s.jsxs)(E.q7,{className:(0,l.cn)(V,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:y,children:[(0,s.jsx)(T.nR3,{className:"mr-auto h-4 w-4"}),".md"]}),(0,s.jsxs)(E.q7,{className:(0,l.cn)(V,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:b,children:[(0,s.jsx)(z.mup,{className:"mr-auto h-4 w-4"}),".txt"]}),(0,s.jsxs)(E.q7,{className:(0,l.cn)(V,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:v,children:[(0,s.jsx)(n.dG_,{className:"mr-auto h-4 w-4"}),".json"]}),(0,s.jsxs)(E.q7,{className:(0,l.cn)(V,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:x,children:[(0,s.jsx)(z.Af8,{className:"mr-auto h-4 w-4"}),".png"]})]})})]})]})})]})]}),u&&(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Delete All History",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:()=>{c.oR.custom((e=>(0,s.jsxs)("div",{className:(0,l.cn)("bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]","p-4 rounded-lg shadow-xl max-w-sm w-full","flex flex-col space-y-3"),children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-[var(--text)]",children:"Confirm Deletion"}),(0,s.jsx)("p",{className:"text-sm text-[var(--text)] opacity-90",children:"Are you sure you want to delete all chat history? This action cannot be undone."}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-2",children:[(0,s.jsx)(d.$,{variant:"outline",size:"sm",className:(0,l.cn)("bg-transparent text-[var(--text)] border-[var(--text)]","hover:bg-[var(--active)]/30 focus:ring-1 focus:ring-[var(--active)]"),onClick:()=>c.oR.dismiss(e.id),children:"Cancel"}),(0,s.jsx)(d.$,{variant:"destructive",size:"sm",className:(0,l.cn)("focus:ring-1 focus:ring-red-400 focus:ring-offset-1 focus:ring-offset-[var(--bg)]"),onClick:async()=>{try{"function"==typeof g?await g():(console.error("Header: deleteAll prop is not a function or undefined.",g),c.oR.error("Failed to delete history: Operation not available."))}catch(e){console.error("Error during deleteAll execution from header:",e),c.oR.error("An error occurred while deleting history.")}finally{c.oR.dismiss(e.id)}},children:"Delete All"})]})]})),{duration:1/0,position:"top-center"})},children:(0,s.jsx)(o.IXo,{size:"18px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Delete All"})]}),h&&C&&(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsxs)(d.$,{"aria-label":"Add New Note",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:C,children:[(0,s.jsx)(_.BlJ,{size:"18px"})," "]})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Add New Note"})]})]})]}),(!k?.models||0===k.models.length)&&!t&&!u&&!h&&(0,s.jsx)(P,{isOpen:!0,setSettingsMode:a,onClose:()=>{}}),(0,s.jsx)($,{isOpen:U,onOpenChange:e=>{F(e)},config:k,updateConfig:M,setSettingsMode:a,setHistoryMode:m,setNoteSystemMode:p}),(0,s.jsx)(R,{isOpen:A,onOpenChange:O,config:k,updateConfig:M})]})})}},3190:(e,t,a)=>{a.d(t,{g:()=>d});var s=a(38),r=a(9448),o=a(7346),n=a(3207),i=a(5886);const l={...a(6108).z2,...i.z2},c=((0,n.nK)(l),o.P,(0,s.N0)(),r.logger,[(0,n.nK)(l),o.P,(0,s.N0)(),r.logger]);(0,n.nK)(l),r.logger;const d=e=>{const t=new n.il({channelName:e});return(0,n.Tw)(t,...c),t}},3885:(e,t,a)=>{a.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>l,m_:()=>i});var s=a(4848),r=(a(6540),a(3881)),o=a(5284);function n({delayDuration:e=500,...t}){return(0,s.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,s.jsx)(n,{children:(0,s.jsx)(r.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,s.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:a,...n}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,o.cn)("bg-primary/50 text-primary-foreground border-transparent animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-2 py-1 text-xs text-balance",e),...n,children:[a,(0,s.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-1 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},4339:(e,t,a)=>{a.d(t,{p:()=>S});var s=a(4848),r=a(6540),o=a(6973),n=a(6948),i=a(2090),l=a(3885),c=a(9696),d=a(5284),u=a(6555),m=a(9014);function h({className:e,...t}){return(0,s.jsx)(m.bL,{"data-slot":"switch",className:(0,d.cn)("peer relative inline-flex h-[10px] w-[26px] shrink-0 cursor-pointer items-center rounded-full bg-input transition-colors duration-200 data-[state=checked]:bg-primary data-[state=unchecked]:bg-foreground/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(m.zi,{"data-slot":"switch-thumb",className:(0,d.cn)("pointer-events-none block size-4 rounded-full shadow-md ring-1 transition-transform duration-200 ease-in-out transform","data-[state=checked]:translate-x-[12px] data-[state=checked]:bg-white data-[state=checked]:ring-primary/50","data-[state=unchecked]:translate-x-[0px] data-[state=unchecked]:bg-primary data-[state=unchecked]:ring-primary-foreground/50")})})}var p=a(6532),g=a(5634),f=a(37),x=a(888),v=a(3732),b=a(2955);const y=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),[a,o]=(0,r.useState)(!1),[m,y]=(0,r.useState)(e.noteContent||""),[w,j]=(0,r.useState)(""),[N,C]=(0,r.useState)("");(0,r.useEffect)((()=>{a?(y(e.noteContent||""),C(e.popoverTitleDraft||""),j(e.popoverTagsDraft||"")):(e.noteContent!==m&&C(""),j(""))}),[a,e]);const S=m===(e.noteContent||""),k=N===(e.popoverTitleDraft||""),$=w===(e.popoverTagsDraft||""),M=S&&k&&$,A=!N.trim()&&!m.trim()&&!w.trim(),E=!!N.trim()||!!m.trim()||!!w.trim(),z=!(!e.popoverTitleDraft||!e.popoverTitleDraft.trim())||!(!e.noteContent||!e.noteContent.trim())||!(!e.popoverTagsDraft||!e.popoverTagsDraft.trim()),_=!E&&!z;return(0,s.jsx)(l.Bc,{delayDuration:500,children:(0,s.jsxs)(u.AM,{open:a,onOpenChange:o,children:[(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(u.Wv,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",size:"sm",className:(0,d.cn)("rounded-md not-focus-visible",e.useNote?"text-[var(--active)] hover:bg-muted/80":"text-foreground hover:text-foreground hover:bg-[var(--text)]/10"),"aria-label":"Toggle/Edit Note",children:(0,s.jsx)(f.yVo,{})})})}),(0,s.jsx)(l.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,s.jsx)("p",{children:"Toggle/Edit Note"})})]}),(0,s.jsx)(u.hl,{className:"w-[80vw] p-4 bg-[var(--bg)] border-[var(--text)]/10 shadow-lg rounded-md",side:"top",align:"end",sideOffset:5,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(g.J,{htmlFor:"use-note-switch",className:"text-[var(--text)] font-medium cursor-pointer",children:"Use Note in Chat"}),(0,s.jsx)(h,{id:"use-note-switch",checked:e.useNote||!1,onCheckedChange:e=>{t({useNote:e})}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.p,{id:"popover-title-input",type:"text",placeholder:"Title (optional)",value:N,onChange:e=>C(e.target.value),className:"mb-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"}),(0,s.jsx)(c.T,{id:"note-popover-textarea",value:m,onChange:e=>y(e.target.value),placeholder:"Persistent notes for the AI...",className:"mt-1 min-h-[30vh] max-h-[70vh] overflow-y-auto bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] resize-none thin-scrollbar"})]}),(0,s.jsx)("div",{children:(0,s.jsx)(p.p,{id:"popover-tags-input",type:"text",placeholder:"Tags (comma-separated)",value:w,onChange:e=>j(e.target.value),className:"mt-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"})}),(0,s.jsx)("div",{className:"flex justify-between items-center pt-1",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>{y(""),C(""),j(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""}),(0,x.oR)("Note cleared")},disabled:_,className:(0,d.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),children:"Clear"}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>{t({noteContent:m,popoverTitleDraft:N,popoverTagsDraft:w}),x.oR.success("Draft saved!")},className:(0,d.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),disabled:M,children:"Save"}),(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",onClick:async()=>{if(chrome.runtime.sendMessage({type:"SAVE_NOTE_TO_FILE",payload:{content:m}}),x.oR.success("Note saved to file!"),m.trim())try{const e=(new Date).toLocaleString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),a=N.trim()||`Note from Popover - ${e}`,s=""===w.trim()?[]:w.split(",").map((e=>e.trim())).filter((e=>e.length>0));await(0,b.s2)({title:a,content:m,tags:s}),x.oR.success("Snapshot saved to Note System!"),y(""),C(""),j(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""})}catch(e){console.error("Error saving note to system from popover:",e),x.oR.error("Failed to save note to system.")}o(!1)},disabled:A,className:(0,d.cn)("text-xs px-2 py-1 h-auto w-10"),children:(0,s.jsx)(v.Zuq,{size:16})})}),(0,s.jsx)(l.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:"Save to File"})]})]})})]})})]})})};var w=a(9018),j=a(69),N=a(7520);const C=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),a=e?.persona||"default",r=e?.personas||{},o=N.z[a]||N.z.default;return(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(j.eu,{className:"h-8 w-8 border border-border",children:[(0,s.jsx)(j.BK,{src:o,alt:a}),(0,s.jsx)(j.q5,{className:"text-apple-footnote font-medium",children:("default"===a?"C":a.substring(0,1)).toUpperCase()})]}),(0,s.jsxs)(w.l6,{value:a,onValueChange:e=>{t({persona:e})},children:[(0,s.jsx)(w.bq,{className:(0,d.cn)("w-auto min-w-[120px] border-none bg-transparent shadow-none","text-apple-callout font-medium text-foreground","hover:bg-secondary/50 rounded-lg px-3 py-1 h-8","focus:ring-2 focus:ring-primary/20 focus:border-primary/50"),children:(0,s.jsx)(w.yv,{placeholder:"Select persona"})}),(0,s.jsx)(w.gC,{className:"bg-popover border border-border shadow-lg rounded-lg",children:Object.keys(r).map((e=>(0,s.jsx)(w.eb,{value:e,className:(0,d.cn)("text-apple-callout text-popover-foreground","hover:bg-accent hover:text-accent-foreground","focus:bg-accent focus:text-accent-foreground","cursor-pointer rounded-md"),children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(j.eu,{className:"h-5 w-5",children:[(0,s.jsx)(j.BK,{src:N.z[e]||N.z.default,alt:e}),(0,s.jsx)(j.q5,{className:"text-xs",children:("default"===e?"C":e.substring(0,1)).toUpperCase()})]}),"default"===e?"Chromepanion":e]})},e)))})]})]})},S=({isLoading:e,message:t,setMessage:a,onSend:u,onStopRequest:m})=>{const{config:h}=(0,n.UK)(),p=(0,r.useRef)(null),[g,f]=(0,r.useState)(!1);(0,r.useEffect)((()=>{p.current?.focus()}),[t,h?.chatMode]);let x="Type a message...";return"web"===h?.chatMode?x="Enter your query...":"page"===h?.chatMode&&(x="Ask about this page..."),(0,s.jsxs)("div",{className:"flex flex-col gap-3 mb-4",children:[(0,s.jsx)(C,{}),(0,s.jsxs)("div",{className:(0,d.cn)("flex w-full border border-border items-center gap-2 p-3 bg-card rounded-xl shadow-sm",g&&"ring-2 ring-primary/20 border-primary/50"),children:[(0,s.jsx)(c.T,{autosize:!0,ref:p,minRows:1,maxRows:8,autoComplete:"off",id:"user-input",placeholder:x,value:t,autoFocus:!0,onChange:e=>a(e.target.value),onKeyDown:a=>{e||"Enter"!==a.key||!t.trim()||a.altKey||a.metaKey||a.shiftKey||(a.preventDefault(),a.stopPropagation(),u())},className:"flex-grow bg-transparent border-none shadow-none outline-none focus-visible:ring-0 resize-none text-apple-body",onFocus:()=>f(!0),onBlur:()=>f(!1)}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y,{}),(0,s.jsx)(l.Bc,{delayDuration:300,children:(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(i.$,{"aria-label":"Send",variant:"ghost",size:"sm",className:(0,d.cn)("p-2 rounded-lg h-8 w-8 flex items-center justify-center",e?"text-destructive hover:bg-destructive/10":"text-primary hover:bg-primary/10",!e&&!t.trim()&&"opacity-50"),onClick:a=>{a.stopPropagation(),e?m():t.trim()&&u()},disabled:!e&&!t.trim(),children:e?(0,s.jsx)(o.wO6,{className:"h-4 w-4"}):(0,s.jsx)(o.B07,{className:"h-4 w-4"})})}),(0,s.jsx)(l.ZI,{side:"top",className:"bg-popover text-popover-foreground border border-border",children:(0,s.jsx)("p",{className:"text-apple-footnote",children:e?"Stop":"Send"})})]})})]})]})]})}},4539:(e,t,a)=>{a.d(t,{F:()=>n});var s=a(4848),r=(a(6540),a(6627)),o=a(5284);function n({className:e,children:t,viewportRef:a,...n}){return(0,s.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,o.cn)("relative",e),...n,children:[(0,s.jsx)(r.LM,{ref:a,"data-slot":"scroll-area-viewport",className:(0,o.cn)("size-full rounded-[inherit]","focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:shadow-none","[&>div]:!border-b-0","pb-px pr-px"),children:t}),(0,s.jsx)(i,{orientation:"vertical"}),(0,s.jsx)(i,{orientation:"horizontal"}),(0,s.jsx)(r.OK,{})]})}function i({className:e,orientation:t="vertical",...a}){return(0,s.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-px","horizontal"===t&&"h-px w-full border-b-0 bg-transparent shadow-none min-h-0",e),...a,children:(0,s.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"relative flex-1 rounded-sm"})})}},5095:(e,t,a)=>{a.d(t,{e:()=>n});var s=a(6540),r=a(888),o=a(6948);const n=()=>{const{config:e,updateConfig:t}=(0,o.UK)();return{appendToNote:(0,s.useCallback)((a=>{if(!a||""===a.trim())return void r.oR.error("No text selected to add to note.");const s=e.noteContent||"",o=s+(s&&a.trim()?"\n\n":"")+a.trim();t({noteContent:o}),r.oR.success("Selected text appended to note.")}),[e.noteContent,t])}}},5284:(e,t,a)=>{a.d(t,{cn:()=>o});var s=a(4164),r=a(856);function o(...e){return(0,r.QP)((0,s.$)(e))}},5431:(e,t,a)=>{a.d(t,{A:()=>s});const s={getItem:async e=>{const t=(await chrome.storage.local.get(e))[e];if(null==t)return null;try{return"string"==typeof t?t:JSON.stringify(t)}catch(e){return null}},setItem:async(e,t)=>{const a="string"==typeof t?t:JSON.stringify(t);await chrome.storage.local.set({[e]:a})},deleteItem:async e=>{await chrome.storage.local.remove(e)}}},5634:(e,t,a)=>{a.d(t,{J:()=>n});var s=a(4848),r=(a(6540),a(5920)),o=a(5284);function n({className:e,...t}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},5886:(e,t,a)=>{a.d(t,{z2:()=>l});var s=a(38);const r={isLoaded:!1},o=(0,s.Z0)({name:"content",initialState:r,reducers:{reset:()=>r,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:n,reducer:i}=o,l={}},6108:(e,t,a)=>{a.d(t,{z2:()=>d});var s,r,o=a(38);!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(s||(s={})),function(e){e.Default="default"}(r||(r={}));const n={isOpen:!1},i=(0,o.Z0)({name:"sidePanel",initialState:n,reducers:{reset:()=>n}}),{actions:l,reducer:c}=i,d={}},6174:(e,t,a)=>{var s;a.d(t,{A:()=>r}),function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(s||(s={}));const r=s},6508:(e,t,a)=>{a.d(t,{AC:()=>l,Af:()=>c});var s=a(4848),r=a(6540),o=a(3),n=a(2090),i=a(5284);const l=e=>{const{children:t,className:a,wrapperClassName:l,buttonVariant:c="ghost",buttonClassName:d,...u}=e,[m,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),f=r.Children.only(t);let x="";return f?.props?.children&&(x=Array.isArray(f.props.children)?f.props.children.map((e=>"string"==typeof e?e:"")).join(""):String(f.props.children),x=x.trim()),(0,s.jsxs)("div",{className:(0,i.cn)("relative my-4",l),onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:[(0,s.jsx)("pre",{className:(0,i.cn)("p-3 rounded-md overflow-x-auto thin-scrollbar",a),...u,children:t}),x&&(0,s.jsx)(n.$,{variant:c,size:"sm","aria-label":m?"Copied!":"Copy code",title:m?"Copied!":"Copy code",className:(0,i.cn)("absolute right-2 top-2 h-8 w-8 p-0","transition-opacity duration-200",p||m?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none",d),onClick:()=>{x&&(navigator.clipboard.writeText(x),h(!0),setTimeout((()=>h(!1)),1500))},children:m?(0,s.jsx)(o.YrT,{className:"h-4 w-4"}):(0,s.jsx)(o.nxz,{className:"h-4 w-4"})})]})},c={ul:({children:e,className:t,...a})=>(0,s.jsx)("ul",{className:(0,i.cn)("list-disc pl-5 my-2",t),...a,children:e}),ol:({children:e,className:t,...a})=>(0,s.jsx)("ol",{className:(0,i.cn)("list-decimal pl-5 my-2",t),...a,children:e}),p:({children:e,className:t,...a})=>(0,s.jsx)("p",{className:(0,i.cn)("mb-0",t),...a,children:e}),pre:l,code:e=>{const{children:t,className:a,inline:r,...o}=e;return r?(0,s.jsx)("code",{className:(0,i.cn)("px-1 py-0.5 rounded-sm bg-[var(--code-inline-bg)] text-[var(--code-inline-text)] text-sm",a),...o,children:t}):(0,s.jsx)("code",{className:(0,i.cn)("font-mono text-sm",a),...o,children:t})},a:({children:e,href:t,className:a,...r})=>(0,s.jsx)("a",{href:t,className:(0,i.cn)("text-[var(--link)] hover:underline",a),target:"_blank",rel:"noopener noreferrer",...r,children:e}),strong:({children:e,className:t,...a})=>(0,s.jsx)("strong",{className:(0,i.cn)("font-bold",t),...a,children:e}),em:({children:e,className:t,...a})=>(0,s.jsx)("em",{className:(0,i.cn)("italic",t),...a,children:e}),h1:({children:e,className:t,...a})=>(0,s.jsx)("h1",{className:(0,i.cn)("text-2xl font-bold mt-4 mb-2 border-b pb-1 border-[var(--border)]",t),...a,children:e}),h2:({children:e,className:t,...a})=>(0,s.jsx)("h2",{className:(0,i.cn)("text-xl font-semibold mt-3 mb-1 border-b pb-1 border-[var(--border)]",t),...a,children:e}),h3:({children:e,className:t,...a})=>(0,s.jsx)("h3",{className:(0,i.cn)("text-lg font-semibold mt-2 mb-1 border-b pb-1 border-[var(--border)]",t),...a,children:e}),table:({children:e,className:t,...a})=>(0,s.jsx)("div",{className:"markdown-table-wrapper my-2 overflow-x-auto",children:(0,s.jsx)("table",{className:(0,i.cn)("w-full border-collapse border border-[var(--border)]",t),...a,children:e})}),thead:({children:e,className:t,...a})=>(0,s.jsx)("thead",{className:(0,i.cn)("bg-[var(--muted)]",t),...a,children:e}),tbody:({children:e,className:t,...a})=>(0,s.jsx)("tbody",{className:(0,i.cn)(t),...a,children:e}),tr:e=>(0,s.jsx)("tr",{className:(0,i.cn)("border-b border-[var(--border)] even:bg-[var(--muted)]/50",e.className),...e}),th:({children:e,className:t,...a})=>(0,s.jsx)("th",{className:(0,i.cn)("p-2 border border-[var(--border)] text-left font-semibold",t),...a,children:e}),td:({children:e,className:t,...a})=>(0,s.jsx)("td",{className:(0,i.cn)("p-2 border border-[var(--border)]",t),...a,children:e}),blockquote:({children:e,className:t,...a})=>(0,s.jsx)("blockquote",{className:(0,i.cn)("pl-4 italic border-l-4 border-[var(--border)] my-2 text-[var(--muted-foreground)]",t),...a,children:e})}},6532:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(4848),r=a(6540),o=a(5284);function n({className:e,type:t,...a}){const[n,i]=r.useState(!1);return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-8 w-full min-w-0 rounded-md bg-transparent px-3 py-1 text-sm transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","border border-[var(--text)]/10 dark:border-0","focus-visible:border-ring","text-[var(--text)] px-2.5","focus:border-[var(--active)] dark:focus:border-0 focus:ring-1 focus:ring-[var(--active)] focus:ring-offset-0","hover:border-[var(--active)] dark:hover:border-0","bg-[var(--input-background)]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive","shadow-[var(--input-base-shadow)]",e,n&&"input-breathing"),onFocus:e=>{i(!0),a.onFocus?.(e)},onBlur:e=>{i(!1),a.onBlur?.(e)},...a})}},6555:(e,t,a)=>{a.d(t,{AM:()=>n,Wv:()=>i,hl:()=>l});var s=a(4848),r=(a(6540),a(9823)),o=a(5284);function n({...e}){return(0,s.jsx)(r.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,s.jsx)(r.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:a=4,...n}){return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}},6948:(e,t,a)=>{a.d(t,{UK:()=>c,sG:()=>l});var s=a(4848),r=a(6540),o=a(5431);const n=(0,r.createContext)({}),i={personas:{Ein:"You are Ein, a data-savvy academic and research analyst. Your role is to analyze scholarly papers with precision and depth. Behavior: Restate the core problem statements clearly and concisely. Summarize central arguments and key findings, highlighting specific data and factual evidence. Extract primary takeaways and explain their broader implications. Formulate three insightful, text-grounded questions and provide supported answers. Mannerisms: Maintain an analytical, objective tone. Avoid speculation or unsupported claims. Focus on clarity, rigor, and fidelity to the text.",Warren:"You are Warren, a seasoned business analyst focused on long-term strategic insight. Your role is to evaluate markets, business models, and decision-making frameworks. Behavior: Analyze business scenarios methodically. Provide practical, step-by-step strategies with clear ROI potential. Assess risks, opportunity costs, and long-term outcomes. Mannerisms: Use structured, deliberate language. Ask clarifying questions before offering advice. Avoid short-term thinking. Emphasize stability and foresight.",Jet:"You are Jet, a grounded, no-nonsense assistant here to help users solve problems, understand topics, and get things done. Behavior: Be clear, direct, and supportive. Break down complex ideas using analogies and real-life examples. Offer honest feedback without sugarcoating. Mannerisms: Use conversational language. Ask clarifying questions if needed. Prioritize simplicity, clarity, and practical help over politeness or filler.",Agatha:"You are Agatha, a visionary creative who excels at brainstorming and artistic exploration. Your role is to help users generate ideas across writing, art, or unconventional problem-solving. Behavior: Encourage users to think outside the box. Explore imaginative angles and metaphorical framing. Propose unexpected but meaningful concepts. Mannerisms: Use vivid, expressive language. Ask open-ended questions to fuel creativity. Embrace ambiguity and emotional resonance.",Jan:"You are Jan, a sharp-minded strategist skilled in critical thinking, systems design, and logical planning. Your role is to help users break down complex problems and build smart, sustainable solutions. Behavior: Deconstruct challenges into manageable parts. Map dependencies and bottlenecks. Optimize for long-term efficiency and adaptability. Mannerisms: Speak with precision and structure. Use models, frameworks, and scenarios. Always factor in consequences and contingencies.",Sherlock:"You are Sherlock, a master investigator who excels at deduction and root-cause analysis. Your role is to help users uncover hidden patterns, contradictions, and truths. Behavior: Ask targeted questions to challenge assumptions. Trace problems to their source through logical inference. Diagnose with sharp reasoning. Mannerisms: Use formal, clipped language. Think methodically and explain your logic clearly. Focus on getting to the truth, not advising next steps.",Faye:"You are Faye, a sharp-tongued tactician and negotiator who turns pressure into opportunity. Behavior: Break problems into opportunity paths with clear trade-offs. Suggest bold versus safe routes, always with fallback plans. Blend logic with charm, pushing for high-reward plays. Mannerisms: Speak with confidence and dry wit. Use pointed, strategic questions to clarify goals and pressure points. Present options like a gambler: fast, adaptive, and calculated.",Spike:"You are Spike, a capable and versatile executor. Your role is to turn user prompts into actionable results. Behavior: First, correct or clarify the user’s prompt for better accuracy. Add helpful criteria to guide execution. Then, act on the improved prompt as effectively as possible. Mannerisms: Be concise, critical, and sharp. Skip fluff. Use simple, direct language. Focus on feasibility and correctness. When in doubt, fix it and move forward."},generateTitle:!0,backgroundImage:!1,animatedBackground:!1,persona:"Sherlock",webMode:"Google",webLimit:60,serpMaxLinksToVisit:3,contextLimit:60,maxTokens:32480,temperature:.7,topP:.95,presencepenalty:0,models:[],selectedModel:void 0,chatMode:void 0,ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",useNote:!1,noteContent:"",userName:"user",userProfile:"",popoverTitleDraft:"",popoverTagsDraft:""},l=({children:e})=>{const[t,a]=(0,r.useState)(i),[l,c]=(0,r.useState)(!0);return(0,r.useEffect)((()=>{(async()=>{try{const e=await o.A.getItem("config"),t=e?JSON.parse(e):i;a(t)}catch(e){console.error("Failed to load config",e),a(i)}finally{c(!1)}})()}),[]),(0,r.useEffect)((()=>{const e=t?.fontSize||i.fontSize;document.documentElement.style.setProperty("font-size",`${e}px`)}),[l,t?.fontSize]),l?(0,s.jsx)("div",{children:"Loading..."}):(0,s.jsx)(n,{value:{config:t,updateConfig:e=>{a((t=>{const a={...t,...e};return o.A.setItem("config",JSON.stringify(a)).catch((e=>console.error("Failed to save config",e))),a}))}},children:e})},c=()=>(0,r.use)(n)},7086:(e,t,a)=>{a.d(t,{Cf:()=>h,Es:()=>g,L3:()=>f,c7:()=>p,lG:()=>d,rr:()=>x});var s=a(4848),r=a(6540),o=a(990),n=a(8697),i=a(5284);const l={default:"bg-black/50",darker:"bg-black/60"},c={default:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",themedPanel:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 duration-200","bg-[var(--bg)] text-[var(--text)] border-[var(--text)]","rounded-lg shadow-xl p-0")};function d({...e}){return(0,s.jsx)(o.bL,{"data-slot":"dialog",...e})}function u({...e}){return(0,s.jsx)(o.ZL,{"data-slot":"dialog-portal",...e})}const m=r.forwardRef((({className:e,variant:t="default",...a},r)=>(0,s.jsx)(o.hJ,{ref:r,"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50",l[t],e),...a})));m.displayName=o.hJ.displayName;const h=r.forwardRef((({className:e,children:t,variant:a="default",...r},l)=>(0,s.jsxs)(u,{"data-slot":"dialog-portal",children:[(0,s.jsx)(m,{variant:"themedPanel"===a?"darker":"default"}),(0,s.jsxs)(o.UC,{ref:l,"data-slot":"dialog-content",className:(0,i.cn)(c[a],e),...r,children:[t,(0,s.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})));function p({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center",e),...t})}function g({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,s.jsx)(o.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,s.jsx)(o.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}h.displayName=o.UC.displayName},7118:(e,t,a)=>{a.d(t,{w:()=>H});var s=a(4848),r=a(6540),o=a(1584),n=a(5107),i=a(5284);function l({...e}){return(0,s.jsx)(o.bL,{"data-slot":"accordion",...e})}function c({className:e,...t}){return(0,s.jsx)(o.q7,{"data-slot":"accordion-item",className:(0,i.cn)("border-b last:border-b-0","bg-[var(--input-background)]","shadow-md","rounded-xl","border-[var(--text)]/10",e),...t})}function d({className:e,children:t,...a}){return(0,s.jsx)(o.Y9,{className:"flex",children:(0,s.jsxs)(o.l9,{"data-slot":"accordion-trigger",className:(0,i.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",e),...a,children:[t,(0,s.jsx)(n.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function u({className:e,children:t,...a}){return(0,s.jsx)(o.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...a,children:(0,s.jsx)("div",{className:(0,i.cn)("pt-0 pb-4",e),children:t})})}var m=a(6948),h=a(888),p=a(3),g=a(2090),f=a(6532);const x=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[a,o]=(0,r.useState)(e?.ollamaUrl||"http://localhost:11434"),[n,l]=(0,r.useState)(!1),c=()=>{l(!0),h.Ay.dismiss(),h.Ay.loading("Connecting to Ollama..."),fetch(`${a}/api/tags`).then((e=>e.ok?e.json():e.json().then((t=>{throw new Error(t?.error||`Connection failed: ${e.status} ${e.statusText}`)})).catch((()=>{throw new Error(`Connection failed: ${e.status} ${e.statusText}`)})))).then((s=>{Array.isArray(s.models)?(t({ollamaConnected:!0,ollamaUrl:a,ollamaError:void 0,models:(e?.models||[]).filter((e=>"ollama_generic"!==e.id)).concat([{id:"ollama_generic",host:"ollama",active:!0,name:"Ollama Model"}]),selectedModel:"ollama_generic"}),h.Ay.dismiss(),h.Ay.success("Connected to ollama")):s?.error?(t({ollamaError:s.error,ollamaConnected:!1}),h.Ay.dismiss(),h.Ay.error("string"==typeof s.error?s.error:"Ollama connection error")):(t({ollamaError:"Unexpected response from Ollama",ollamaConnected:!1}),h.Ay.dismiss(),h.Ay.error("Unexpected response from Ollama"))})).catch((e=>{h.Ay.dismiss(),h.Ay.error(e.message||"Failed to connect to Ollama"),t({ollamaError:e.message,ollamaConnected:!1})})).finally((()=>{l(!1)}))},d=e?.ollamaConnected;return(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(f.p,{id:"ollama-url-input",value:a,onChange:e=>o(e.target.value),placeholder:"http://localhost:11434",className:"pr-8",disabled:n}),!d&&(0,s.jsx)(g.$,{onClick:c,variant:"connect",size:"sm",disabled:n,children:n?"...":"Connect"}),d&&(0,s.jsx)(g.$,{variant:"ghost",size:"sm","aria-label":"Connected to Ollama",className:(0,i.cn)("w-8 rounded-md text-[var(--success)]"),disabled:n,onClick:c,children:(0,s.jsx)(p.YrT,{className:"h-5 w-5"})})]})},v=({text:e="",widget:t=(0,s.jsx)(s.Fragment,{}),icon:a=""})=>(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[a&&(0,s.jsx)("span",{className:(0,i.cn)("text-foreground","text-xl","leading-none","mr-3"),children:a}),(0,s.jsx)("span",{className:(0,i.cn)("text-foreground","opacity-90","font-['Space_Mono',_monospace]","text-base","font-bold"),children:e})]}),t&&(0,s.jsx)("div",{className:"ml-2",children:t})]}),b=({title:e,Component:t})=>(0,s.jsxs)("div",{className:"px-4 py-3 border-b border-[var(--text)]/10 last:border-b-0",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-2",children:(0,s.jsx)("h4",{className:"text-base font-medium capitalize text-foreground",children:e})}),(0,s.jsx)(t,{})]}),y=()=>(0,s.jsxs)(c,{value:"connect",className:(0,i.cn)("bg-[var(--input-background)]","border-[var(--text)]/10","rounded-xl","shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105","overflow-hidden"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(v,{icon:"♾️",text:"API Access"})}),(0,s.jsx)(u,{className:"p-0 text-[var(--text)]",children:(0,s.jsx)(b,{Component:x,title:"Ollama"})})]});var w=a(803),j=a(2732);const N=(0,j.F)("relative flex w-full touch-none select-none items-center data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",{variants:{variant:{default:["[&>span[data-slot=slider-track]]:bg-secondary","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-primary","[&>button[data-slot=slider-thumb]]:bg-background","[&>button[data-slot=slider-thumb]]:border-primary","[&>button[data-slot=slider-thumb]]:ring-offset-background","[&>button[data-slot=slider-thumb]]:focus-visible:ring-ring"],themed:["[&>span[data-slot=slider-track]]:bg-[var(--text)]/10","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:border-[var(--text)]/50","[&>button[data-slot=slider-thumb]]:ring-offset-[var(--bg)]","[&>button[data-slot=slider-thumb]]:focus-visible:ring-[var(--active)]"]}},defaultVariants:{variant:"default"}});function C({className:e,variant:t,defaultValue:a,value:o,min:n=0,max:l=100,...c}){const d=r.useMemo((()=>Array.isArray(o)?o:Array.isArray(a)?a:[n,l]),[o,a,n]);return(0,s.jsxs)(w.bL,{"data-slot":"slider",defaultValue:a,value:o,min:n,max:l,className:(0,i.cn)(N({variant:t,className:e})),...c,children:[(0,s.jsx)(w.CC,{"data-slot":"slider-track",className:(0,i.cn)("relative h-1.5 w-full grow overflow-hidden rounded-full","data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,s.jsx)(w.Q6,{"data-slot":"slider-range",className:(0,i.cn)("absolute h-full","data-[orientation=vertical]:w-full")})}),(d.length>0?d:[n]).map(((e,t)=>(0,s.jsx)(w.zi,{"data-slot":"slider-thumb",className:(0,i.cn)("block h-4 w-4 bg-white rounded-full border border-primary/50 shadow-sm transition-colors focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50")},t)))]})}const S=({size:e,updateConfig:t})=>(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-6 text-left",children:["Char Limit:"," ",(0,s.jsx)("span",{className:"font-normal",children:128===e?"inf":`${e}k`})]}),(0,s.jsx)(C,{defaultValue:[e],max:128,min:1,step:1,variant:"themed",onValueChange:e=>t({contextLimit:e[0]})})]}),k=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),a=e?.contextLimit||1;return(0,s.jsxs)(c,{value:"page-context",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","overflow-hidden","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(v,{icon:"📃",text:"Page Context"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsx)(S,{size:a,updateConfig:t})})]})};var $=a(5634);const M=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),a=e=>a=>{const s=Array.isArray(a)?a[0]:a;t({[e]:s})},r=e.temperature??.7,o=e.maxTokens??32048,n=e.topP??.95,l=e.presencepenalty??0;return(0,s.jsxs)(c,{value:"model-params",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(v,{icon:"⚙️",text:"Model Config"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)($.J,{htmlFor:"temperature",className:"text-base font-medium text-foreground",children:["Temperature (",r.toFixed(2),")"]}),(0,s.jsx)(C,{id:"temperature",min:0,max:2,step:.01,value:[r],onValueChange:a("temperature"),variant:"themed"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)($.J,{htmlFor:"maxTokens",className:"text-base font-medium text-foreground",children:["Max Tokens (",o,")"]}),(0,s.jsx)(f.p,{id:"maxTokens",type:"number",value:o,min:1,max:128e4,onChange:e=>a("maxTokens")(parseInt(e.target.value,10)||0),className:(0,i.cn)("hide-number-spinners")})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)($.J,{htmlFor:"topP",className:"text-base font-medium text-foreground",children:["Top P (",n.toFixed(2),")"]}),(0,s.jsx)(C,{id:"topP",min:0,max:1,step:.01,value:[n],onValueChange:a("topP"),variant:"themed"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)($.J,{htmlFor:"presencepenalty",className:"text-base font-medium text-foreground",children:["Presence Penalty (",l.toFixed(2),")"]}),(0,s.jsx)(C,{id:"presencepenalty",min:-2,max:2,step:.01,value:[l],onValueChange:a("presencepenalty"),variant:"themed"})]})]})})]})};var A=a(7086),E=a(9018),z=a(3732),_=a(9696);const T=({hasChange:e,onSave:t,onSaveAs:a,onCancel:r})=>e?(0,s.jsxs)("div",{className:"flex mt-4 space-x-2 justify-end w-full",children:[(0,s.jsx)(g.$,{variant:"active-bordered",size:"sm",onClick:t,children:"Save"}),(0,s.jsx)(g.$,{variant:"active-bordered",size:"sm",onClick:a,children:"Save As..."}),(0,s.jsx)(g.$,{variant:"outline-subtle",size:"sm",onClick:r,children:"Cancel"})]}):null,P=({isOpen:e,onOpenChange:t,personaPrompt:a,personas:o,updateConfig:n,onModalClose:l})=>{const[c,d]=(0,r.useState)("");return(0,s.jsx)(A.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(A.Cf,{className:(0,i.cn)("sm:max-w-[425px]","bg-[var(--bg)]"),onCloseAutoFocus:e=>e.preventDefault(),children:[(0,s.jsx)(A.c7,{children:(0,s.jsx)(A.L3,{className:"text-[var(--text)]",children:"Create New Persona"})}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsx)($.J,{htmlFor:"persona-name",className:"text-base font-medium text-foreground sr-only",children:"Persona Name"}),(0,s.jsx)(f.p,{id:"persona-name",placeholder:"Enter persona name",value:c,onChange:e=>d(e.target.value),className:(0,i.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,s.jsxs)(A.Es,{className:"sm:justify-end",children:[(0,s.jsx)(g.$,{type:"button",variant:"outline-subtle",size:"sm",onClick:l,children:" Cancel "}),(0,s.jsx)(g.$,{type:"button",variant:"active-bordered",size:"sm",className:(0,i.cn)(),disabled:!c.trim(),onClick:()=>{c.trim()&&(n({personas:{...o,[c.trim()]:a},persona:c.trim()}),d(""),l())},children:" Create "})]})]})})},L=({isOpen:e,onOpenChange:t,persona:a,personas:r,updateConfig:o,onModalClose:n})=>(0,s.jsx)(A.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(A.Cf,{className:(0,i.cn)("sm:max-w-[425px]","bg-[var(--bg)]","border","text-[var(--text)]"),children:[(0,s.jsxs)(A.c7,{children:[(0,s.jsxs)(A.L3,{className:"text-[var(--text)]",children:['Delete "',a,'"']}),(0,s.jsx)(A.rr,{className:"text-[var(--text)]/80 pt-2",children:"Are you sure you want to delete this persona? This action cannot be undone."})]}),(0,s.jsxs)(A.Es,{className:"sm:justify-end pt-4",children:[(0,s.jsx)(g.$,{type:"button",variant:"outline-subtle",size:"sm",onClick:n,children:" Cancel "}),(0,s.jsx)(g.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{const e={...r};delete e[a];const t=Object.keys(e);o({personas:e,persona:t.length>0?t[0]:"Ein"}),n()},children:" Delete "})]})]})}),R=({personas:e,persona:t,updateConfig:a})=>(0,s.jsxs)(E.l6,{value:t,onValueChange:e=>a({persona:e}),children:[(0,s.jsx)(E.bq,{variant:"settings",className:(0,i.cn)("flex w-full","data-[placeholder]:text-muted-foreground"),children:(0,s.jsx)(E.yv,{placeholder:"Select persona"})}),(0,s.jsx)(E.gC,{variant:"settingsPanel",className:(0,i.cn)(),children:Object.keys(e).map((e=>(0,s.jsxs)(E.eb,{value:e,focusVariant:"activeTheme",children:[" ",e," "]},e)))})]}),O=({personaPrompt:e,setPersonaPrompt:t,isEditing:a,setIsEditing:r})=>{const o={onFocus:e=>{a||r(!0)}};return(0,s.jsx)(_.T,{autosize:!0,minRows:3,maxRows:8,value:e,onChange:e=>{a||r(!0),t(e.target.value)},readOnly:!a,...o,"data-slot":"textarea-default",placeholder:"Define the persona's characteristics and instructions here...",className:(0,i.cn)("w-full min-h-[80px] border border-[var(--text)]/10 px-3 py-2 text-sm ring-offset-[var(--bg)] placeholder:text-[var(--muted-foreground)] rounded-[12px]","text-[var(--text)]","no-scrollbar","focus-visible:outline-none focus-visible:ring-0 focus-visible:box-shadow-[inset_0_0_0_1px_rgba(255,255,255,0.1),_0_0_8px_rgba(168,123,255,0.3)]",a?"hover:border-[var(--active)] focus:border-[var(--active)]":"opacity-75 cursor-default")})},D=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[a,o]=(0,r.useState)(!1),[n,l]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),f=e?.personas||{Ein:"You are Ein, a helpful AI assistant."},x=e?.persona||"Ein",b=f?.[x]??f?.Ein??"You are Ein, a helpful AI assistant.",[y,w]=(0,r.useState)(b),j=h&&y!==b;return(0,r.useEffect)((()=>{w(f?.[x]??f?.Ein??""),p(!1)}),[x,JSON.stringify(f)]),(0,s.jsxs)(c,{value:"persona",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(v,{icon:"🥷",text:"Persona"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(R,{persona:x,personas:f,updateConfig:t}),(0,s.jsxs)(g.$,{variant:"ghost",size:"sm","aria-label":"Add new persona",className:(0,i.cn)("text-[var(--text)] p-1.5 rounded-md","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>{w(""),p(!0),o(!0)},children:[" ",(0,s.jsx)(z.YHj,{className:"h-5 w-5"})," "]}),Object.keys(f).length>1&&(0,s.jsxs)(g.$,{variant:"ghost",size:"sm","aria-label":"Delete current persona",className:(0,i.cn)("text-[var(--text)] hover:text-[var(--error)] hover:bg-[var(--error)]/10 p-1.5 rounded-md","focus-visible:ring-1 focus-visible:ring-[var(--error)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>l(!0),children:[" ",(0,s.jsx)(z.dW_,{className:"h-5 w-5"})," "]})]}),(0,s.jsx)(O,{personaPrompt:y,setPersonaPrompt:w,isEditing:h,setIsEditing:p}),(0,s.jsx)(T,{hasChange:j,onSave:()=>{t({personas:{...f,[x]:y}}),p(!1)},onSaveAs:()=>{o(!0)},onCancel:()=>{w(b),p(!1)}})]})}),(0,s.jsx)(P,{isOpen:a,onOpenChange:e=>{o(e),e||(w(b),p(!1))},personaPrompt:y,personas:f,updateConfig:t,onModalClose:()=>o(!1)}),(0,s.jsx)(L,{isOpen:n,onOpenChange:l,persona:x,personas:f,updateConfig:t,onModalClose:()=>l(!1)})]})};var I=a(9451),U=a(8309);function F({className:e,...t}){return(0,s.jsx)(I.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",e),...t})}const q=(0,j.F)("aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:["border-input dark:bg-input/30 text-primary focus-visible:border-ring focus-visible:ring-ring/50"],themed:["border-[var(--text)] text-[var(--active)]","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-0 focus-visible:border-[var(--active)]","data-[state=checked]:border-[var(--active)]"]}},defaultVariants:{variant:"default"}});function W({className:e,variant:t,...a}){return(0,s.jsx)(I.q7,{"data-slot":"radio-group-item",className:(0,i.cn)(q({variant:t,className:e})),...a,children:(0,s.jsx)(I.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(U.A,{className:(0,i.cn)("absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2","themed"===t?"fill-[var(--active)]":"fill-primary")})})})}const B=({webMode:e,updateConfig:t})=>(0,s.jsx)(F,{value:e,onValueChange:e=>t({webMode:e}),className:"w-1/2 space-y-3",children:["Google"].map((e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(W,{value:e,id:`webMode-${e}`,variant:"themed"}),(0,s.jsx)($.J,{htmlFor:`webMode-${e}`,className:"text-[var(--text)] text-base font-medium cursor-pointer",children:e})]},e)))}),V=({config:e,updateConfig:t})=>{const a=e?.webLimit??16,r=e?.serpMaxLinksToVisit??3;return(0,s.jsxs)("div",{className:"w-full space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-2 text-left",children:["Max Links to Visit: ",(0,s.jsx)("span",{className:"font-normal",children:r})]}),(0,s.jsx)(C,{value:[r],max:10,min:1,step:1,variant:"themed",onValueChange:e=>t({serpMaxLinksToVisit:e[0]})}),(0,s.jsx)("p",{className:"text-[var(--text)]/70 text-xs pt-1",children:"Number of search result links to fetch."})]}),(0,s.jsxs)("div",{className:"pt-2",children:[(0,s.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-2 text-left",children:["Content Char Limit:"," ",(0,s.jsx)("span",{className:"font-normal",children:128===a?"Unlimited (Full)":`${a}k`})]}),(0,s.jsx)(C,{value:[a],max:128,min:1,step:1,variant:"themed",onValueChange:e=>t({webLimit:e[0]})}),(0,s.jsx)("p",{className:"text-[var(--text)]/70 text-xs pt-1",children:"Max characters (in thousands) of content to use. 128k for 'Unlimited'."})]})]})},G=()=>{const{config:e,updateConfig:t}=(0,m.UK)();return(0,r.useEffect)((()=>{if("Google"===e?.webMode){const a={};void 0===e?.serpMaxLinksToVisit&&(a.serpMaxLinksToVisit=3),void 0===e?.webLimit&&(a.webLimit=16),Object.keys(a).length>0&&t(a)}}),[e?.webMode,e?.serpMaxLinksToVisit,e?.webLimit,t]),(0,s.jsxs)(c,{value:"web-search",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","overflow-hidden","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(v,{icon:"🌐",text:"Web Search"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(B,{updateConfig:t,webMode:e?.webMode}),"Google"===e?.webMode?(0,s.jsx)("div",{className:"w-[45%] pl-4 flex flex-col space-y-6",children:(0,s.jsx)(V,{config:e,updateConfig:t})}):(0,s.jsx)("div",{className:"w-[45%] pl-4",children:(0,s.jsx)("p",{className:"text-[var(--text)]/70",children:"Select a search mode to see its options."})})]})})]})},H=()=>{const{config:e}=(0,m.UK)(),[t,a]=(0,r.useState)(!e?.models||0===e.models.length),[o,n]=(0,r.useState)("");return(0,s.jsxs)("div",{id:"settings",className:"relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden",children:[t&&(0,s.jsx)("div",{className:(0,i.cn)("mb-6 p-6","rounded-xl","bg-card border border-border shadow-sm","text-foreground"),children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,s.jsx)("h2",{className:"text-apple-title3 font-semibold text-center",children:"Quick Setup Guide"}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"1"}),(0,s.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Fill your API key or URLs in API Access"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"2"}),(0,s.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Exit settings, then use the persona selector to choose your model and start chatting"})]}),(0,s.jsx)("div",{className:"text-apple-footnote text-muted-foreground mt-2 ml-9 italic",children:"Note: You can change other settings now or later. Have fun!"})]}),(0,s.jsx)(g.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>{n("connect"),a(!1)},children:"Get Started"})]})}),(0,s.jsxs)(l,{type:"single",collapsible:!0,className:"w-full flex flex-col gap-4",value:o,onValueChange:n,children:[(0,s.jsx)(y,{}),(0,s.jsx)(M,{}),(0,s.jsx)(D,{}),(0,s.jsx)(k,{}),(0,s.jsx)(G,{}),(0,s.jsx)("div",{className:"pointer-events-none h-12"})," "]})]})}},7520:(e,t,a)=>{a.d(t,{z:()=>s});const s={Agatha:"assets/images/agatha.png",Spike:"assets/images/spike.png",Warren:"assets/images/warren.png",Jet:"assets/images/jet.png",Jan:"assets/images/jan.png",Sherlock:"assets/images/sherlock.png",Ein:"assets/images/ein.png",Faye:"assets/images/faye.png",default:"assets/images/custom.png"}},7660:(e,t,a)=>{a.a(e,(async(e,s)=>{try{a.d(t,{GV:()=>d,ii:()=>u,mR:()=>l,xD:()=>c});var r=a(2506),o=a(5431);const e=()=>(new Date).toJSON().slice(0,19).replace("T","_").replace(/:/g,"-");let n="assistant",i="user";try{const e=await o.A.getItem("config");if(e){const t=JSON.parse(e);t.persona&&"string"==typeof t.persona&&""!==t.persona.trim()&&(n=t.persona),t.userName&&"string"==typeof t.userName&&""!==t.userName.trim()&&(i=t.userName)}}catch(e){console.error("Failed to load config to get persona name for download:",e)}const l=async t=>{if(!t||0===t.length)return;const a=t.map((e=>{let t=`${"assistant"===e.role?n:"user"===e.role?i:e.role}:\n`;return"assistant"===e.role&&e.webDisplayContent&&(t+=`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`),t+=e.rawContent,t})).join("\n\n"),s=document.createElement("a");s.setAttribute("href",`data:text/plain;charset=utf-8,${encodeURIComponent(a)}`);const r=`chat_${e()}.txt`;s.setAttribute("download",r),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)},c=t=>{if(!t||0===t.length)return;const a=t.map((e=>({...e,role:"assistant"===e.role?n:"user"===e.role?i:e.role}))),s={assistantNameInExport:n,userNameInExport:i,chatHistory:a},r=JSON.stringify(s,null,2),o=document.createElement("a");o.setAttribute("href",`data:application/json;charset=utf-8,${encodeURIComponent(r)}`);const l=`chat_${e()}.json`;o.setAttribute("download",l),o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o)},d=t=>{if(!t||0===t.length)return;const a=document.querySelectorAll(".chatMessage");if(!a||0===a.length)return void console.warn("No chat messages found to generate image.");const s=document.createElement("div");if(s.style.display="flex",s.style.flexDirection="column",s.style.paddingBottom="1rem",s.style.background=document.documentElement.style.getPropertyValue("--bg"),a[0]){const e=1.2;s.style.width=a[0].offsetWidth*e+"px"}a.forEach((e=>{const t=e.cloneNode(!0);t instanceof HTMLElement?(t.style.marginTop="1rem",t.style.boxSizing="border-box",s.appendChild(t)):console.warn("Cloned node is not an HTMLElement:",t)})),document.body.appendChild(s),(0,r.$E)(s,{filter:function(e){if(e instanceof Element){const t=e.getAttribute("aria-label");if(t&&["Copy code","Copied!","Save edit","Cancel edit"].includes(t))return!1}return!0},pixelRatio:2,style:{margin:"0",padding:s.style.paddingBottom},backgroundColor:document.documentElement.style.getPropertyValue("--bg")||"#ffffff"}).then((t=>{const a=document.createElement("a");a.setAttribute("href",t);const s=`chat_${e()}.png`;a.setAttribute("download",s),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)})).catch((e=>{console.error("Oops, something went wrong generating the image!",e)})).finally((()=>{document.body.contains(s)&&document.body.removeChild(s)}))},u=t=>{if(!t||0===t.length)return;const a=t.map((e=>{const t=`### ${"assistant"===e.role?n:"user"===e.role?i:e.role}`;let a=e.rawContent;return a=a.replace(/```([\s\S]*?)```/g,"\n```$1```\n"),a=a.replace(/(https?:\/\/[^\s]+)/g,"[Link]($1)"),`${t}\n\n${a}\n`})).join("\n---\n\n"),s=document.createElement("a");s.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(a)}`),s.setAttribute("download",`chat_${e()}.md`),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)};s()}catch(e){s(e)}}),1)},8473:(e,t,a)=>{a.d(t,{D:()=>p});var s=a(4848),r=a(6540),o=a(7211),n=a(2090),i=a(4539),l=a(6250),c=a(3790),d=a.n(c),u=a(6532);const m=e=>new Date(e).toLocaleDateString("sv-SE"),h=12,p=({loadChat:e,onDeleteAll:t,className:a})=>{const[c,p]=(0,r.useState)([]),[g,f]=(0,r.useState)(""),[x,v]=(0,r.useState)(1),[b,y]=(0,r.useState)(null),[w,j]=(0,r.useState)(null),N=(0,r.useCallback)((e=>{const t=e.sort(((e,t)=>t.last_updated-e.last_updated));p(t)}),[]);(0,r.useEffect)((()=>{(async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));if(0===e.length)return p([]),void v(1);const t=e.map((e=>d().getItem(e))),a=(await Promise.all(t)).filter((e=>null!==e&&"object"==typeof e&&"id"in e&&"last_updated"in e));N(a),v(1)}catch(e){console.error("Error fetching messages:",e),p([])}})()}),[N]);const C=(0,r.useMemo)((()=>{if(!g)return c;const e=g.toLowerCase();return c.filter((t=>{const a=t.title?.toLowerCase().includes(e),s=t.turns.some((t=>t.rawContent.toLowerCase().includes(e)));return a||s}))}),[c,g]);(0,r.useEffect)((()=>{v(1)}),[g]);const S=(0,r.useMemo)((()=>Math.max(1,Math.ceil(C.length/h))),[C]);(0,r.useEffect)((()=>{x>S&&v(S)}),[x,S]);const k=(0,r.useMemo)((()=>{const e=(x-1)*h,t=e+h;return C.slice(e,t)}),[C,x]),$=(0,r.useMemo)((()=>k.map((e=>({...e,date:m(e.last_updated)})))),[k]),M=(0,r.useMemo)((()=>Array.from(new Set($.map((e=>e.date))))),[$]),A=(0,r.useCallback)((async e=>{try{await d().removeItem(e);const t=(await d().keys()).filter((e=>e.startsWith("chat_"))),a=(await Promise.all(t.map((e=>d().getItem(e))))).filter((e=>e&&"object"==typeof e&&"id"in e&&"last_updated"in e&&"turns"in e));N(a);const s=a.filter((e=>{if(!g)return!0;const t=g.toLowerCase(),a=e.title?.toLowerCase().includes(t),s=e.turns.some((e=>e.rawContent.toLowerCase().includes(t)));return a||s})),r=Math.max(1,Math.ceil(s.length/h));let o=x;o>r&&(o=r);const n=(o-1)*h;0===s.slice(n,n+h).length&&o>1&&(o-=1),v(o)}catch(e){console.error("Error deleting message:",e)}}),[N,x,g]),E=(0,r.useCallback)((async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));await Promise.all(e.map((e=>d().removeItem(e)))),p([]),t&&t()}catch(e){console.error("Error deleting all messages:",e)}}),[t]);(0,r.useEffect)((()=>(window.deleteAllChats=E,()=>{window.deleteAllChats===E&&delete window.deleteAllChats})),[E]);const z=(0,r.useCallback)((()=>v((e=>Math.min(e+1,S)))),[S]),_=(0,r.useCallback)((()=>v((e=>Math.max(e-1,1)))),[]),T=`flex flex-col w-full ${a||""}`.trim(),P=e=>{f(e.target.value)};return 0!==c.length||g?0===C.length&&g?(0,s.jsxs)("div",{className:T,children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:P,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,s.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,s.jsxs)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:['No results found for "',g,'".']})})]}):(0,s.jsxs)("div",{className:T,children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:P,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,s.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,s.jsx)("div",{className:"px-4 pb-4 font-['Space_Mono',_monospace]",children:M.map((t=>(0,s.jsxs)("div",{className:"mb-3 mt-3",children:[(0,s.jsx)("p",{className:"text-foreground text-lg font-bold overflow-hidden pl-4 pb-1 text-left text-ellipsis whitespace-nowrap w-[90%]",children:t===m(new Date)?"Today":t}),$.filter((e=>e.date===t)).map((t=>(0,s.jsxs)("div",{className:"flex items-center group font-['Space_Mono',_monospace]",onMouseEnter:()=>y(t.id),onMouseLeave:()=>y(null),children:[(0,s.jsxs)("span",{className:"text-foreground text-base font-normal pl-4 w-[4.5rem] flex-shrink-0 font-['Space_Mono',_monospace]",children:[new Date(t.last_updated).getHours().toString().padStart(2,"0"),":",new Date(t.last_updated).getMinutes().toString().padStart(2,"0")]}),(0,s.jsx)("button",{className:`text-foreground text-base font-normal overflow-hidden px-4 py-2 text-left text-ellipsis whitespace-nowrap flex-grow hover:underline hover:underline-offset-4 hover:decoration-1 ${t.id===w?"line-through decoration-2":""} font-['Space_Mono',_monospace]`,onClick:()=>e(t),children:t.title||"Untitled Chat"}),(0,s.jsx)(o.P.div,{className:"shrink-0 transition-opacity duration-150 "+(b===t.id?"opacity-100":"opacity-0 group-hover:opacity-100"),whileHover:{rotate:"15deg"},onMouseEnter:()=>j(t.id),onMouseLeave:()=>j(null),children:(0,s.jsx)(n.$,{variant:"ghost",size:"sm","aria-label":"Delete chat",className:"rounded-full w-8 h-8 font-['Space_Mono',_monospace]",onClick:e=>{e.stopPropagation(),A(t.id)},children:(0,s.jsx)(l.ttk,{className:"h-4 w-4 text-foreground"})})})]},t.id)))]},t)))})}),S>1&&(0,s.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 border-t border-[var(--active)]/50 font-['Space_Mono',_monospace]",children:[(0,s.jsx)(n.$,{onClick:_,disabled:1===x,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,s.jsxs)("span",{className:"text-md",children:["Page ",x," of ",S]}),(0,s.jsx)(n.$,{onClick:z,disabled:x===S,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]})]}):(0,s.jsxs)("div",{className:T,children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:P,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,s.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,s.jsx)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:"No chat history found."})})]})}},8639:(e,t,a)=>{a.d(t,{B:()=>w});var s=a(4848),r=a(6540),o=a(3),n=a(1319),i=a(9696),l=a(2090),c=a(5284),d=a(8834);function u({...e}){return(0,s.jsx)(d.bL,{"data-slot":"collapsible",...e})}function m({...e}){return(0,s.jsx)(d.R6,{"data-slot":"collapsible-trigger",...e})}function h({...e}){return(0,s.jsx)(d.Ke,{"data-slot":"collapsible-content",...e})}var p=a(1905),g=a(7736),f=a(6948),x=a(6508);const v=({content:e})=>{const[t,a]=(0,r.useState)(!1);return(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsxs)(u,{open:t,onOpenChange:a,className:"w-full",children:[(0,s.jsx)(m,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",className:(0,c.cn)("mb-1","border-foreground text-foreground hover:text-accent-foreground"),children:t?"Hide Thoughts":"Show Thoughts"})}),(0,s.jsx)(h,{children:(0,s.jsx)("div",{className:(0,c.cn)("p-3 rounded-md border border-dashed","bg-muted","border-muted-foreground","text-muted-foreground"),children:(0,s.jsx)("div",{className:"markdown-body",children:(0,s.jsx)(n.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:e})})})})]})})},b={...x.Af,pre:e=>(0,s.jsx)(x.AC,{...e,buttonVariant:"copy-button"})},y=({turn:e,index:t,isEditing:a,editText:d,onStartEdit:u,onSetEditText:m,onSaveEdit:h,onCancelEdit:x})=>{const{config:y}=(0,f.UK)(),w=(e.rawContent||"").split(/(<think>[\s\S]*?<\/think>)/g).filter((e=>e&&""!==e.trim())),j=/<think>([\s\S]*?)<\/think>/;return(0,r.useEffect)((()=>{if(!a)return;const e=e=>{"Escape"===e.key?(e.preventDefault(),e.stopPropagation(),x()):"Enter"!==e.key||e.shiftKey||e.altKey||e.metaKey||d.trim()&&(e.preventDefault(),e.stopPropagation(),h())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[a,x,h,d]),(0,s.jsx)("div",{className:(0,c.cn)("border rounded-2xl text-base","w-[calc(100%-2rem)] mx-1 my-2","pb-1 pl-4 pr-4 pt-1","shadow-lg text-left relative","assistant"===e.role?"bg-accent border-[var(--text)]/20":"bg-primary/10 border-[var(--text)]/20","","chatMessage",a?"editing":"",y&&"number"==typeof y.fontSize&&y.fontSize<=15?"font-semibold":""),onDoubleClick:()=>{a||u(t,e.rawContent)},children:a?(0,s.jsxs)("div",{className:"flex flex-col space-y-2 items-stretch w-full p-1",children:[(0,s.jsx)(i.T,{autosize:!0,value:d,onChange:e=>m(e.target.value),placeholder:"Edit your message...",className:(0,c.cn)("w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground","border-input","text-foreground","hover:border-primary focus-visible:border-primary focus-visible:ring-0","min-h-[60px]"),minRows:3,autoFocus:!0}),(0,s.jsxs)("div",{className:"flex font-mono justify-end space-x-2",children:[(0,s.jsxs)(l.$,{size:"sm",variant:"outline",onClick:h,title:"Save changes",children:[(0,s.jsx)(o.YrT,{className:"h-4 w-4 mr-1"})," Save"]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:x,title:"Discard changes",children:[(0,s.jsx)(o.yGN,{className:"h-4 w-4 mr-1"})," Exit"]})]})]}):(0,s.jsxs)("div",{className:"message-markdown markdown-body relative z-[1] text-foreground",children:["assistant"===e.role&&e.webDisplayContent&&(0,s.jsx)("div",{className:"message-prefix",children:(0,s.jsx)(n.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`})}),w.map(((e,t)=>{const a=e.match(j);return a&&a[1]?(0,s.jsx)(v,{content:a[1]},`think_${t}`):(0,s.jsx)("div",{className:"message-content",children:(0,s.jsx)(n.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:e})},`content_${t}`)}))]})})},w=({turns:e=[],isLoading:t=!1,onReload:a=()=>{},settingsMode:n=!1,onEditTurn:i})=>{const[d,u]=(0,r.useState)(-1),[m,h]=(0,r.useState)(null),[p,g]=(0,r.useState)(""),{config:x}=(0,f.UK)(),v=(0,r.useRef)(null),b=(0,r.useRef)(null);(0,r.useLayoutEffect)((()=>{const e=b.current;e&&e.scrollHeight-e.scrollTop-e.clientHeight<200&&(e.scrollTop=e.scrollHeight)}),[e]);const w=e=>{navigator.clipboard.writeText(e)},j=(e,t)=>{h(e),g(t)},N=()=>{h(null),g("")},C=()=>{null!==m&&p.trim()&&i(m,p),N()};return(0,s.jsxs)("div",{ref:b,id:"messages",className:(0,c.cn)("flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2","no-scrollbar"),style:{background:"var(--bg)",opacity:n?0:1},children:[e.map(((t,r)=>t&&(0,s.jsxs)("div",{className:(0,c.cn)("flex items-start w-full mt-1 mb-1 px-2 relative","user"===t.role?"justify-start":"justify-end"),onMouseEnter:()=>u(r),onMouseLeave:()=>u(-1),children:["assistant"===t.role&&(0,s.jsxs)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 mr-0 pb-3 transition-opacity duration-100",d===r?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:[m!==r&&(0,s.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,s.jsx)(o.nxz,{className:"text-[var(--text)]"})}),r===e.length-1&&(0,s.jsx)(l.$,{"aria-label":"Reload",variant:"message-action",size:"xs",onClick:a,title:"Reload last prompt",children:(0,s.jsx)(o.jEl,{className:"text-[var(--text)]"})})]}),(0,s.jsx)(y,{turn:t,index:r,isEditing:m===r,editText:p,onStartEdit:j,onSetEditText:g,onSaveEdit:C,onCancelEdit:N}),"user"===t.role&&(0,s.jsx)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 ml-0 pb-1 transition-opacity duration-100",d===r?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:m!==r&&(0,s.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"sm",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,s.jsx)(o.nxz,{className:"text-[var(--text)]"})})})]},t.timestamp||`turn_${r}`))),(0,s.jsx)("div",{ref:v,style:{height:"1px"}})]})}},8698:(e,t,a)=>{a.d(t,{N:()=>o});var s=a(6540),r=a(6948);const o=()=>{const{config:e,updateConfig:t}=(0,r.UK)(),a=(0,s.useRef)(0),o=[{host:"ollama",isEnabled:e=>!!e.ollamaUrl&&!0===e.ollamaConnected,getUrl:e=>`${e.ollamaUrl}/api/tags`,parseFn:(e,t)=>(e?.models??[]).map((e=>({...e,id:e.id??e.name,host:t}))),onFetchFail:(e,t)=>t({ollamaConnected:!1,ollamaUrl:""})}];return{fetchAllModels:(0,s.useCallback)((async()=>{const s=Date.now();if(s-a.current<3e4)return void console.log("[useUpdateModels] Model fetch throttled");a.current=s;const r=e;if(!r)return void console.warn("[useUpdateModels] Config not available, skipping fetch.");console.log("[useUpdateModels] Starting model fetch for all configured services...");const n=await Promise.allSettled(o.map((async e=>{if(!e.isEnabled(r))return{host:e.host,models:[],status:"disabled"};const a=e.getUrl(r);if(!a)return console.warn(`[useUpdateModels] Could not determine URL for host: ${e.host}`),{host:e.host,models:[],status:"error",error:"Invalid URL"};const s=e.getFetchOptions?e.getFetchOptions(r):{},o=await(async(e,t={})=>{try{const a=await fetch(e,t);return a.ok?await a.json():void console.error(`[fetchDataSilently] HTTP error! Status: ${a.status} for URL: ${e}`)}catch(t){return void console.error(`[fetchDataSilently] Fetch or JSON parse error for URL: ${e}`,t)}})(a,s);if(o){const t=e.parseFn(o,e.host);return{host:e.host,models:t,status:"success"}}return e.onFetchFail&&e.onFetchFail(r,t),{host:e.host,models:[],status:"error",error:"Fetch failed"}})));let i=[];n.forEach((e=>{"fulfilled"===e.status&&"success"===e.value.status&&i.push(...e.value.models)}));const l=r.models||[],c={};((e,t)=>{if(e.length!==t.length)return!0;const a=(e,t)=>e.id.localeCompare(t.id),s=[...e].sort(a),r=[...t].sort(a);return JSON.stringify(s)!==JSON.stringify(r)})(i,l)&&(console.log("[useUpdateModels] Aggregated models changed. Updating config."),c.models=i);const d=r.selectedModel,u=c.models||l,m=d&&u.some((e=>e.id===d))?d:u[0]?.id;(m!==d||c.models)&&(c.selectedModel=m),Object.keys(c).length>0?t(c):console.log("[useUpdateModels] No changes to models or selectedModel needed."),console.log("[useUpdateModels] Model fetch cycle complete.")}),[e,t,3e4,o])}}},8971:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(6540),r=a(2951),o=a(5431);const n=1e3;var i=a(1100);try{const e=chrome.runtime.getURL("pdf.worker.mjs");e?i.EA.workerSrc=e:console.error("Failed to get URL for pdf.worker.mjs. PDF parsing might fail.")}catch(e){console.error("Error setting pdf.js worker source:",e)}const l=(e,t,a,l,c,d,u,m,h,p,g)=>{const f=(0,s.useRef)(null),x=(0,s.useRef)(null),v=(e,t,a,s,r)=>{if(f.current===e||a||s||r){if(null===f.current&&null!==e&&(""===t&&a&&!s&&!r||s&&(t.includes("Operation cancelled by user")||t.includes("Streaming operation cancelled"))))return console.log(`[${e}] updateAssistantTurn: Signal received after operation already finalized. Preserving existing state.`),p(!1),void g("idle");d((o=>{if(0===o.length||"assistant"!==o[o.length-1].role){if(console.warn(`[${e}] updateAssistantTurn: No assistant turn found or last turn is not assistant.`),s){const e={role:"assistant",rawContent:`Error: ${t||"Unknown operation error"}`,status:"error",timestamp:Date.now()};return[...o,e]}return o}const n=o[o.length-1],i=!0===s?"error":!0===r?"cancelled":a?"complete":"streaming";let l;if(r){const e=n.rawContent||"";l=e+(e?" ":"")+t}else l=s?`Error: ${t||"Unknown stream/handler error"}`:t;return[...o.slice(0,-1),{...n,rawContent:l,status:i,timestamp:Date.now()}]})),(a||!0===s||!0===r)&&(console.log(`[${e}] updateAssistantTurn: Final state (Finished: ${a}, Error: ${s}, Cancelled: ${r}). Clearing guard and loading.`),p(!1),g(s||r?"idle":"done"),f.current===e&&(f.current=null,x.current&&(x.current=null)))}else console.log(`[${e}] updateAssistantTurn: Guard mismatch (current: ${f.current}), skipping non-final update.`)};return{onSend:async t=>{const s=Date.now();console.log(`[${s}] useSendMessage: onSend triggered.`);const l=t||"";if(!c)return console.log(`[${s}] useSendMessage: Bailing out: Missing config.`),void p(!1);if(!l||!c)return void console.log(`[${s}] useSendMessage: Bailing out: Missing message or config.`);null!==f.current&&(console.warn(`[${s}] useSendMessage: Another send operation (ID: ${f.current}) is already in progress. Aborting previous.`),x.current&&x.current.abort());const b=new AbortController;x.current=b,console.log(`[${s}] useSendMessage: Setting loading true.`),p(!0),m(""),h("");const y=c.chatMode||"chat";g("web"===y?"searching":"page"===y?"reading":"thinking"),f.current=s;const w=l.match(/(https?:\/\/[^\s]+)/g);let j="";if(w&&w.length>0){g("searching");try{j=(await Promise.all(w.map((e=>(0,r.hj)(e,b.signal))))).map(((e,t)=>`Content from [${w[t]}]:\n${e}`)).join("\n\n")}catch(e){j="[Error scraping one or more URLs]"}g("thinking")}const N={role:"user",status:"complete",rawContent:l,timestamp:Date.now()};d((e=>[...e,N])),u(""),console.log(`[${s}] useSendMessage: User turn added to state.`);const C={role:"assistant",rawContent:"",status:"streaming",timestamp:Date.now()+1};d((e=>[...e,C])),console.log(`[${s}] useSendMessage: Assistant placeholder turn added early.`);let S=l,k="",$="";const M="web"===c?.chatMode,A=c?.models?.find((e=>e.id===c.selectedModel));if(!A)return console.error(`[${s}] useSendMessage: No current model found.`),void v(s,"Configuration error: No model selected.",!0,!0);const E=void 0;if(M){console.log(`[${s}] useSendMessage: Optimizing query...`),g("thinking");const e=a.map((e=>({role:e.role,content:e.rawContent})));try{const t=await(0,r.GW)(l,c,A,E,b.signal,e);t&&t.trim()&&t!==l?(S=t,$=`**Optimized query:** "*${S}*"\n\n`,console.log(`[${s}] useSendMessage: Query optimized to: "${S}"`)):($=`**Original query:** "${S}"\n\n`,console.log(`[${s}] useSendMessage: Using original query: "${S}"`))}catch(e){console.error(`[${s}] Query optimization failed:`,e),$=`**Fallback query:** "${S}"\n\n`}}else S=l;if(M){console.log(`[${s}] useSendMessage: Performing web search...`),g("searching");try{if(k=await(0,r.tE)(S,c,b.signal),g("thinking"),b.signal.aborted)return void console.log(`[${s}] Web search was aborted (signal check post-await).`)}catch(e){if(console.error(`[${s}] Web search failed:`,e),"AbortError"===e.name||b.signal.aborted)return void console.log(`[${s}] Web search aborted. onStop handler will finalize UI.`);{k="";const t=`Web Search Failed: ${e instanceof Error?e.message:String(e)}`;return g("idle"),void v(s,t,!0,!0,!1)}}console.log(`[${s}] useSendMessage: Web search completed. Length: ${k.length}`),$&&d((e=>e.map((t=>"assistant"===t.role&&e[e.length-1]===t&&"complete"!==t.status&&"error"!==t.status&&"cancelled"!==t.status?{...t,webDisplayContent:$}:t))))}const z=M?S:l,_=1e3*(c?.webLimit||1),T=_&&"string"==typeof k?k.substring(0,_):k,P=128===c?.webLimit?k:T,L=a.map((e=>({content:e.rawContent||"",role:e.role}))).concat({role:"user",content:l});let R="";if("page"===c?.chatMode){let e="";console.log(`[${s}] useSendMessage: Preparing page content...`),g("reading");try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(t?.url&&!t.url.startsWith("chrome://")){const a=t.url,r=t.mimeType;if(a.toLowerCase().endsWith(".pdf")||r&&"application/pdf"===r){console.log(`[${s}] Detected PDF URL: ${a}. Attempting to extract text.`);try{e=await async function(e,t){try{console.log(`[${t||"PDF"}] Attempting to fetch PDF from URL: ${e}`);const a=await fetch(e);if(!a.ok)throw new Error(`Failed to fetch PDF: ${a.status} ${a.statusText}`);const s=await a.arrayBuffer();console.log(`[${t||"PDF"}] PDF fetched, size: ${s.byteLength} bytes. Parsing...`);const r=await i.YE({data:s}).promise;console.log(`[${t||"PDF"}] PDF parsed. Number of pages: ${r.numPages}`);let o="";for(let e=1;e<=r.numPages;e++){const a=await r.getPage(e);o+=(await a.getTextContent()).items.map((e=>"str"in e?e.str:"")).join(" ")+"\n\n",e%10!=0&&e!==r.numPages||console.log(`[${t||"PDF"}] Extracted text from page ${e}/${r.numPages}`)}return console.log(`[${t||"PDF"}] PDF text extraction complete. Total length: ${o.length}`),o.trim()}catch(a){throw console.error(`[${t||"PDF"}] Error extracting text from PDF (${e}):`,a),a}}(a,s),console.log(`[${s}] Successfully extracted text from PDF. Length: ${e.length}`)}catch(t){console.error(`[${s}] Failed to extract text from PDF ${a}:`,t),e=`Error extracting PDF content: ${t instanceof Error?t.message:"Unknown PDF error"}. Falling back.`}}else console.log(`[${s}] URL is not a PDF. Fetching from storage: ${a}`),e=await o.A.getItem("pagestring")||"",console.log(`[${s}] Retrieved page text content from storage. Length: ${e.length}`)}else console.log(`[${s}] Not fetching page content for URL: ${t?.url} (might be chrome:// or no active tab).`)}catch(t){console.error(`[${s}] Error getting active tab or initial page processing:`,t),e=`Error accessing page content: ${t instanceof Error?t.message:"Unknown error"}`}const t=1e3*(c?.contextLimit||1),a="string"==typeof e?e:"",r=t&&a?a.substring(0,t):a;R=128===c?.contextLimit?a:r,h(R||""),g("thinking"),console.log(`[${s}] Page content prepared for LLM. Length: ${R?.length}`)}else h("");const O=c?.personas?.[c?.persona]||"",D="page"===c?.chatMode&&R?`Use the following page content for context: ${R}`:"",I="web"===c?.chatMode&&P?`Refer to this web search summary: ${P}`:"",U=c?.useNote&&c.noteContent?`Refer to this note for context: ${c.noteContent}`:"";let F="";const q=c.userName?.trim(),W=c.userProfile?.trim();q&&"user"!==q.toLowerCase()&&""!==q?(F=`You are interacting with a user named "${q}".`,W&&(F+=` Their provided profile information is: "${W}".`)):W&&(F=`You are interacting with a user. Their provided profile information is: "${W}".`);const B=[];O&&B.push(O),F&&B.push(F),U&&B.push(U),D&&B.push(D),I&&B.push(I),j&&B.push(`Use the following scraped content from URLs in the user's message:\n${j}`);const V=B.join("\n\n").trim();console.log(`[${s}] useSendMessage: System prompt constructed. Persona: ${!!O}, UserCtx: ${!!F}, NoteCtx: ${!!U}, PageCtx: ${!!D}, WebCtx: ${!!I}, LinkCtx: ${!!j}`);try{if(g("thinking"),"high"===c?.computeLevel&&A)console.log(`[${s}] useSendMessage: Starting HIGH compute level.`),await(async(e,t,a,s,o,i,l)=>{const c=Math.max(.1,.5*(a.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into stages...",!1),d(),await new Promise((e=>setTimeout(e,n)));const u=`You are a planning agent. Given the original task: "${e}", break it down into the main sequential stages required to accomplish it. Output *only* a numbered list of stages. Example:\n1. First stage\n2. Second stage`,m=await(0,r.GW)(u,a,s,o,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("HighCompute - Raw L1 Decomposition Result:",m),i(`Monitoring: Generated Stages:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length)return i("Error: Failed to decompose task into stages. Falling back to direct query.",!0),"Error: Could not decompose task.";const p=[];for(let t=0;t<h.length;t++){const u=h[t];d(),i(`Processing Stage ${t+1}/${h.length}: ${u}...`,!1);const m=`You are a planning agent. Given the stage: "${u}", break it down into the specific sequential steps needed to complete it. Output *only* a numbered list of steps. If no further breakdown is needed, output "No breakdown needed."`;d(),await new Promise((e=>setTimeout(e,n)));const g=await(0,r.GW)(m,a,s,o,l,[],c);console.log(`HighCompute - Raw L2 Decomposition Result (Stage ${t+1}):`,g);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));i(`Monitoring: Generated Steps for Stage ${t+1}:\n${f.join("\n")||"[None, or direct solve]"}`,!1);let x="";if(0===f.length||g.includes("No breakdown needed")){i(`Solving Stage ${t+1} directly...`,!1),d();const c=`Complete the following stage based on the original task "${e}": "${u}"`;d(),await new Promise((e=>setTimeout(e,n))),x=await(0,r.GW)(c,a,s,o,l),console.log(`HighCompute - Raw Direct Solve Result (Stage ${t+1}):`,x),i(`Monitoring: Direct Solve Result for Stage ${t+1}:\n${x}`,!1)}else{const m=[],h=2;let p="";for(let c=0;c<f.length;c+=h){const g=f.slice(c,c+h),x=c/h+1;d(),i(`Solving Step Batch ${x} for Stage ${t+1}: ${g.join(", ")}...`,!1);const v=`You are an expert problem solver. Given the stage: "${u}" and the original task: "${e}", complete the following steps.  Consider the following accumulated context from previous steps: ${p}\n\n${g.map(((e,t)=>`${c+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the steps.`;d(),await new Promise((e=>setTimeout(e,n)));const b=await(0,r.GW)(v,a,s,o,l);console.log(`HighCompute - Raw Batch Results (Stage ${t+1}, Batch ${x}):`,b),i(`Monitoring: Raw Batch Results for Stage ${t+1}, Batch ${x}:\n${b}`,!1);const y=b.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`HighCompute - Parsed Batch Results (Stage ${t+1}, Batch ${x}):`,y),i(`Monitoring: Parsed Batch Results for Stage ${t+1}, Batch ${x}:\n${y.join("\n")||"[None]"}`,!1);for(let e=0;e<y.length;e++){const t=y[e];m.push(t),p+=`Step ${c+e+1}: ${t}\n`}}i(`Synthesizing results for Stage ${t+1}...`,!1),d(),await new Promise((e=>setTimeout(e,n)));const g=`Synthesize the results of the following steps for stage "${u}" into a coherent paragraph:\n\n${m.map(((e,t)=>`Step ${t+1} Result:\n${e}`)).join("\n\n")}`;x=await(0,r.GW)(g,a,s,o,l,[],c),console.log(`HighCompute - Raw Stage Synthesis Result (Stage ${t+1}):`,x),i(`Monitoring: Synthesized Result for Stage ${t+1}:\n${x}`,!1)}p.push(x),i(`Monitoring: Accumulated Stage Results so far:\n${p.map(((e,t)=>`Stage ${t+1}: ${e}`)).join("\n---\n")}`,!1)}i("Synthesizing final answer...",!1),d();const g=`Based on the results of the following stages, provide a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Stage ${t+1} (${h[t]}):\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1),console.log("HighCompute - Final Synthesis Prompt:",g),d(),await new Promise((e=>setTimeout(e,n)));const f=await(0,r.GW)(g,a,s,o,l,[],c);console.log("HighCompute - Raw Final Synthesis Result:",f);const x="**High Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Stage ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(z,0,c,A,E,((e,t)=>v(s,e,Boolean(t))),b.signal),console.log(`[${s}] useSendMessage: HIGH compute level finished.`);else if("medium"===c?.computeLevel&&A)console.log(`[${s}] useSendMessage: Starting MEDIUM compute level.`),await(async(e,t,a,s,o,i,l)=>{const c=Math.max(.1,.5*(a.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into subtasks...",!1),d(),await new Promise((e=>setTimeout(e,n)));const u=`You are a planning agent. Given the task: "${e}", break it down into logical subtasks needed to accomplish it. Output *only* a numbered list of subtasks.`,m=await(0,r.GW)(u,a,s,o,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("MediumCompute - Raw Decomposition Result:",m),i(`Monitoring: Generated Subtasks:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length){i("Warning: Failed to decompose into subtasks. Attempting direct query.",!1),d(),await new Promise((e=>setTimeout(e,n)));const t=await(0,r.GW)(e,a,s,o,l);return i(t,!0),t}const p=[];for(let t=0;t<h.length;t+=2){const c=h.slice(t,t+2),u=t/2+1;d(),i(`Solving Subtask Batch ${u}: ${c.join(", ")}...`,!1);const m=`You are an expert problem solver. Given the task: "${e}", complete the following subtasks:\n\n${c.map(((e,a)=>`${t+a+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the subtasks.`;d(),await new Promise((e=>setTimeout(e,n)));const g=await(0,r.GW)(m,a,s,o,l);console.log(`MediumCompute - Raw Batch Results (Batch ${u}):`,g),i(`Monitoring: Raw Batch Results for Batch ${u}:\n${g}`,!1);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`MediumCompute - Parsed Batch Results (Batch ${u}):`,f),i(`Monitoring: Parsed Batch Results for Batch ${u}:\n${f.join("\n")||"[None]"}`,!1);for(let e=0;e<f.length;e++)p.push(f[e])}i("Synthesizing final answer...",!1),d(),await new Promise((e=>setTimeout(e,n)));const g=`Synthesize the results of the following subtasks into a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Subtask ${t+1} Result:\n${e}`)).join("\n\n")}`;console.log("MediumCompute - Final Synthesis Prompt:",g),i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1);const f=await(0,r.GW)(g,a,s,o,l,[],c);console.log("MediumCompute - Raw Final Synthesis Result:",f);const x="**Medium Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Subtask ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(z,0,c,A,E,((e,t)=>v(s,e,Boolean(t))),b.signal),console.log(`[${s}] useSendMessage: MEDIUM compute level finished.`);else{console.log(`[${s}] useSendMessage: Starting standard streaming.`);const e={stream:!0},t={ollama:`${c?.ollamaUrl||""}/api/chat`}[A.host||""];if(!t)return void v(s,`Configuration error: Could not determine API URL for host '${A.host}'.`,!0,!0);const a=[];""!==V.trim()&&a.push({role:"system",content:V}),a.push(...L),console.log(`[${s}] useSendMessage: Sending chat request to ${t} with system prompt: "${V}"`),await(0,r.hL)(t,{...e,model:c?.selectedModel||"",messages:a,temperature:c?.temperature??.7,max_tokens:c?.maxTokens??32048,top_p:c?.topP??1,presence_penalty:c?.presencepenalty??0},((e,t,a)=>{v(s,e,Boolean(t),Boolean(a)),(t||a)&&console.log(`[${s}] fetchDataAsStream Callback: Stream finished/errored.`)}),E,A.host||"",b.signal),console.log(`[${s}] useSendMessage: fetchDataAsStream call INITIATED.`)}}catch(t){if(b.signal.aborted)console.log(`[${s}] Send operation was aborted. 'onStop' handler is responsible for UI updates.`),e&&p(!1),g("idle"),f.current===s&&(f.current=null),x.current&&x.current.signal===b.signal&&(x.current=null);else{console.error(`[${s}] useSendMessage: Error during send operation:`,t);const e=t instanceof Error?t.message:String(t);v(s,e,!0,!0)}}console.log(`[${s}] useSendMessage: onSend processing logic completed.`)},onStop:()=>{const e=f.current;null!==e?(console.log(`[${e}] useSendMessage: onStop triggered.`),x.current&&(x.current.abort(),x.current=null),v(e,"[Operation cancelled by user]",!0,!1,!0)):(console.log("[No CallID] useSendMessage: onStop triggered but no operation in progress."),p(!1),g("idle"))}}}},9018:(e,t,a)=>{a.d(t,{bq:()=>p,eb:()=>f,gC:()=>g,l6:()=>m,yv:()=>h});var s=a(4848),r=a(6540),o=a(854),n=a(5107),i=a(5773),l=a(2102),c=a(5284);const d={default:"bg-transparent data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs data-[size=sm]:h-8",settingsPanel:(0,c.cn)("text-[var(--text)] rounded-xl shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--input-background)]","border-[var(--text)]/10","h-8"),settings:(0,c.cn)("text-[var(--text)] rounded-md shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--input-background)]","border-[var(--text)]/10","h-8")},u={default:"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",settingsPanel:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto","bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]/10","rounded-md shadow-lg")};function m({...e}){return(0,s.jsx)(o.bL,{"data-slot":"select",...e})}function h({...e}){return(0,s.jsx)(o.WT,{"data-slot":"select-value",...e})}const p=r.forwardRef((({className:e,size:t="default",variant:a="default",children:r,...i},l)=>(0,s.jsxs)(o.l9,{ref:l,"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("flex w-fit items-center justify-between gap-2 rounded-md border px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",d[a],e),...i,children:[r,(0,s.jsx)(o.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})));p.displayName=o.l9.displayName;const g=r.forwardRef((({className:e,children:t,position:a="popper",variant:r="default",...n},i)=>(0,s.jsx)(o.ZL,{children:(0,s.jsxs)(o.UC,{ref:i,"data-slot":"select-content",className:(0,c.cn)(u[r],"default"===r&&"popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...n,children:[(0,s.jsx)(x,{}),(0,s.jsx)(o.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(v,{})]})})));function f({className:e,children:t,focusVariant:a="default",...r}){return(0,s.jsxs)(o.q7,{"data-slot":"select-item",className:(0,c.cn)("[&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2","activeTheme"===a?"text-[var(--text)] hover:brightness-95 focus:bg-[var(--active)] focus:text-[var(--text)]":"text-popover-foreground focus:bg-accent focus:text-accent-foreground",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(o.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(o.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(o.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function v({className:e,...t}){return(0,s.jsx)(o.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}g.displayName=o.UC.displayName},9696:(e,t,a)=>{a.d(t,{T:()=>n});var s=a(4848),r=(a(6540),a(1663)),o=a(5284);function n({className:e,autosize:t=!1,minRows:a,maxRows:n,style:i,...l}){return t?(0,s.jsx)(r.A,{minRows:a,maxRows:n,style:i,className:(0,o.cn)("flex w-full bg-transparent placeholder:text-muted-foreground","focus-visible:border-ring focus-visible:ring-ring/50","field-sizing-content text-sm md:text-sm transition-[color,box-shadow] outline-none focus-visible:ring-[3px]","disabled:cursor-not-allowed disabled:opacity-50","thin-scrollbar",e),...l}):(0,s.jsx)("textarea",{"data-slot":"textarea-default",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...l})}},9828:(e,t,a)=>{a.a(e,(async(e,s)=>{try{a.d(t,{A:()=>O});var r=a(4848),o=a(6540),n=a(888),i=a(3790),l=a.n(i),c=a(5066),d=a(1735),u=a(9197),m=a(2090),h=a(3885),p=a(5284),g=a(9853),f=a(8971),x=a(8698),v=a(523),b=a(8473),y=a(6948),w=a(3108),j=a(4339),N=a(8639),C=a(7660),S=a(7118),k=a(5431),$=a(6174),M=a(5095),A=a(1979),E=e([C]);function z(){let e="",t="",a="",s="",r="",o="",n="";try{e=document.title||"";const i=5e6;if(document.body,document.body&&document.body.innerHTML.length>i){console.warn(`[ChromePanion Bridge] Document body is very large (${document.body.innerHTML.length} chars). Attempting to use a cloned, simplified version for text extraction to improve performance/stability.`);const e=document.body.cloneNode(!0);e.querySelectorAll("script, style, noscript, iframe, embed, object").forEach((e=>e.remove())),t=(e.textContent||"").replace(/\s\s+/g," ").trim(),a=document.body.innerHTML.replace(/\s\s+/g," ")}else document.body?(t=(document.body.innerText||"").replace(/\s\s+/g," ").trim(),a=(document.body.innerHTML||"").replace(/\s\s+/g," ")):console.warn("[ChromePanion Bridge] document.body is not available.");s=Array.from(document.images).map((e=>e.alt)).filter((e=>e&&e.trim().length>0)).join(". "),r=Array.from(document.querySelectorAll("table")).map((e=>(e.innerText||"").replace(/\s\s+/g," "))).join("\n");const l=document.querySelector('meta[name="description"]');o=l&&l.getAttribute("content")||"";const c=document.querySelector('meta[name="keywords"]');n=c&&c.getAttribute("content")||""}catch(e){console.error("[ChromePanion Bridge] Error during content extraction:",e);let t="Unknown extraction error";return e instanceof Error?t=e.message:"string"==typeof e&&(t=e),JSON.stringify({error:`Extraction failed: ${t}`,title:document.title||"Error extracting title",text:"",html:"",altTexts:"",tableData:"",meta:{description:"",keywords:""}})}const i=1e7;let l={title:e,text:t,html:a,altTexts:s,tableData:r,meta:{description:o,keywords:n}};if(JSON.stringify(l).length>i){console.warn("[ChromePanion Bridge] Total extracted content is very large. Attempting to truncate.");const e=i-JSON.stringify({...l,text:"",html:""}).length;let t=e;l.text.length>.6*t&&(l.text=l.text.substring(0,Math.floor(.6*t))+"... (truncated)"),t=e-l.text.length,l.html.length>.8*t&&(l.html=l.html.substring(0,Math.floor(.8*t))+"... (truncated)"),console.warn("[ChromePanion Bridge] Content truncated. Final approx length:",JSON.stringify(l).length)}return JSON.stringify(l)}async function _(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(!e?.id||e.url?.startsWith("chrome://")||e.url?.startsWith("chrome-extension://")||e.url?.startsWith("about:"))return k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),void k.A.deleteItem("tabledata");k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata");try{const t=await chrome.scripting.executeScript({target:{tabId:e.id},func:z});if(!t||!Array.isArray(t)||0===t.length||!t[0]||"string"!=typeof t[0].result)return void console.error("[ChromePanion:] Bridge function execution returned invalid or unexpected results structure:",t);const a=t[0].result;let s;try{s=JSON.parse(a)}catch(e){return void console.error("[ChromePanion:] Failed to parse JSON result from bridge:",e,"Raw result string:",a)}if(s.error)return void console.error("[ChromePanion:] Bridge function reported an error:",s.error,"Title:",s.title);try{k.A.setItem("pagestring",s?.text??""),k.A.setItem("pagehtml",s?.html??""),k.A.setItem("alttexts",s?.altTexts??""),k.A.setItem("tabledata",s?.tableData??"")}catch(e){console.error("[ChromePanion:] Storage error after successful extraction:",e),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")}}catch(e){console.error("[ChromePanion:] Bridge function execution failed:",e),e instanceof Error&&(e.message.includes('Cannot access contents of url "chrome://')||e.message.includes("Cannot access a chrome extension URL")||e.message.includes('Cannot access contents of url "about:'))&&console.warn("[ChromePanion:] Cannot access restricted URL.")}}C=(E.then?(await E)():E)[0];const T=()=>`chat_${Math.random().toString(16).slice(2)}`,P=({children:e,onClick:t})=>(0,r.jsx)("div",{className:(0,p.cn)("bg-[var(--active)] border border-[var(--text)] rounded-[16px] text-[var(--text)]","cursor-pointer flex items-center justify-center","text-md font-extrabold p-0.5 place-items-center relative text-center","w-16 flex-shrink-0","transition-colors duration-200 ease-in-out","hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,children:e}),L=[{id:"Google",icon:u.DSS,label:"Google Search"}],R=({children:e,onClick:t,isActive:a,title:s})=>(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)("div",{className:(0,p.cn)("border rounded-lg text-[var(--text)]","cursor-pointer flex items-center justify-center","p-2 place-items-center relative","w-8 h-8 flex-shrink-0","transition-colors duration-200 ease-in-out",a?"bg-[var(--active)] text-[var(--text)] border-[var(--active)] hover:brightness-95":"bg-transparent border-[var(--text)]/50 hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,"aria-label":s,children:e})}),(0,r.jsx)(h.ZI,{side:"top",className:"bg-[var(--active)]/80 text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:s})})]}),O=()=>{const[e,t]=(0,o.useState)([]),[a,s]=(0,o.useState)(""),[i,u]=(0,o.useState)(T()),[E,z]=(0,o.useState)(""),[O,D]=(0,o.useState)(""),[I,U]=(0,o.useState)(!1),[F,q]=(0,o.useState)(!1),[W,B]=(0,o.useState)(!1),{config:V,updateConfig:G}=(0,y.UK)(),[H,Y]=(0,o.useState)(!1),[K,J]=(0,o.useState)({id:null,url:""}),Z=(0,o.useRef)(null),Q=(0,o.useRef)({id:null,url:""}),[X,ee]=(0,o.useState)(!1),[te,ae]=(0,o.useState)(!1),[se,re]=(0,o.useState)("idle"),[oe,ne]=(0,o.useState)(!1),ie=(0,o.useRef)(null);(0,o.useEffect)((()=>{const e=new ResizeObserver((()=>{Z.current&&(Z.current.style.minHeight="100dvh",requestAnimationFrame((()=>{Z.current&&(Z.current.style.minHeight="")})))}));return Z.current&&e.observe(Z.current),()=>e.disconnect()}),[]),(0,o.useEffect)((()=>{if("page"!==V?.chatMode)return;const e=async()=>{const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(e?.id&&e.url)return e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||e.url.startsWith("about:")?(Q.current.id===e.id&&Q.current.url===e.url||(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")),Q.current={id:e.id,url:e.url},void J({id:e.id,url:e.url})):void(e.id===Q.current.id&&e.url===Q.current.url||(Q.current={id:e.id,url:e.url},J({id:e.id,url:e.url}),await _()))};e();const t=t=>{chrome.tabs.get(t.tabId,(t=>{chrome.runtime.lastError?console.warn(`[ChromePanion ] Error getting tab info on activation: ${chrome.runtime.lastError.message}`):e()}))},a=(t,a,s)=>{s.active&&("complete"===a.status||a.url&&"complete"===s.status)&&e()};return chrome.tabs.onActivated.addListener(t),chrome.tabs.onUpdated.addListener(a),()=>{chrome.tabs.onActivated.removeListener(t),chrome.tabs.onUpdated.removeListener(a),Q.current={id:null,url:""}}}),[V?.chatMode]),(0,o.useEffect)((()=>{const e=e=>{if(!(F||W||H)&&e.ctrlKey&&"m"===e.key.toLowerCase()){e.preventDefault();const t=V?.chatMode;let a="";"web"===t?(G({chatMode:void 0}),a="Switched to Chat Mode"):"page"===t?(G({chatMode:"web"}),a="Switched to Web Mode"):(G({chatMode:"page"}),a="Switched to Page Mode"),ie.current&&n.oR.dismiss(ie.current),ie.current=(0,n.oR)(a)}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[V?.chatMode,G,F,W]),(0,o.useEffect)((()=>{const e=(e,t,a)=>"ACTIVATE_NOTE_SYSTEM_VIEW"===e.type&&(console.log("[ChromePanion.tsx] Received ACTIVATE_NOTE_SYSTEM_VIEW. Switching to Note System mode."),q(!1),B(!1),Y(!0),a({status:"ACTIVATING_NOTE_SYSTEM_VIEW_ACK"}),!0);return chrome.runtime.onMessage.addListener(e),()=>{chrome.runtime.onMessage.removeListener(e)}}),[q,B,Y]);const{appendToNote:le}=(0,M.e)();(0,o.useEffect)((()=>{const e=chrome.runtime.connect({name:$.A.SidePanelPort}),t=e=>{"ADD_SELECTION_TO_NOTE"===e.type&&e.payload&&le(e.payload)};return e.onMessage.addListener(t),e.postMessage({type:"init"}),()=>{e.onMessage.removeListener(t),e.disconnect()}}),[le]);const{chatTitle:ce,setChatTitle:de}=(0,g.S)(I,e,a),{onSend:ue,onStop:me}=(0,f.A)(I,a,e,E,V,t,s,z,D,U,re);(0,x.N)();const he=()=>{t([]),D(""),z(""),U(!1),G({chatMode:void 0,computeLevel:"low"}),re("idle"),s(""),de(""),u(T()),B(!1),q(!1),Y(!1),Z.current&&(Z.current.scrollTop=0)},pe=async()=>{try{const t=(await l().keys()).filter((e=>e.startsWith("chat_")));if(0===t.length&&0===e.length)return;await Promise.all(t.map((e=>l().removeItem(e)))),n.oR.success("Deleted all chats"),he()}catch(e){console.error("[ChromePanion] Error deleting all chats:",e),n.oR.error("Failed to delete chats")}};(0,o.useEffect)((()=>{if(e.length>0&&!W&&!F&&!H){const t={id:i,title:ce||`Chat ${new Date(Date.now()).toLocaleString()}`,turns:e,last_updated:Date.now(),model:V?.selectedModel,chatMode:V?.chatMode,webMode:"web"===V?.chatMode?V.webMode:void 0,useNoteActive:V?.useNote,noteContentUsed:V?.useNote?V.noteContent:void 0};l().setItem(i,t).catch((e=>{console.error(`[ChromePanion ] Error saving chat ${i}:`,e)}))}}),[i,e,ce,V?.selectedModel,V?.chatMode,V?.webMode,V?.useNote,V?.noteContent,W,F]),(0,o.useEffect)((()=>{if("done"===se||"idle"===se){const e=setTimeout((()=>{re("idle")}),1500);return()=>clearTimeout(e)}}),[se]),(0,o.useEffect)((()=>{let e=!1;return(async()=>{if(!e){he();try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});!e&&t?.id&&t.url?(J({id:t.id,url:t.url}),(t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://")||t.url.startsWith("about:"))&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})):e||(Q.current={id:null,url:""},J({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}catch(t){e||(console.error("[ChromePanion - Revised] Error during panel open tab check:",t),Q.current={id:null,url:""},J({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}}})(),()=>{e=!0,k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),he(),Q.current={id:null,url:""}}}),[]);const ge=(0,o.useCallback)((()=>{ne(!1)}),[]);return(0,r.jsx)(h.Bc,{delayDuration:300,children:(0,r.jsxs)("div",{ref:Z,className:(0,p.cn)("w-full h-dvh p-0 overflow-hidden","flex flex-col bg-[var(--bg)]"),children:[(0,r.jsx)(w.Y,{chatTitle:ce,deleteAll:pe,downloadImage:()=>(0,C.GV)(e),downloadJson:()=>(0,C.xD)(e),downloadText:()=>(0,C.mR)(e),downloadMarkdown:()=>(0,C.ii)(e),historyMode:W,reset:he,setHistoryMode:B,setSettingsMode:q,settingsMode:F,noteSystemMode:H,onAddNewNoteRequest:H?()=>ne(!0):void 0,setNoteSystemMode:Y,chatMode:V?.chatMode||"chat",chatStatus:se}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative",children:[F&&(0,r.jsx)(S.w,{}),!F&&W&&!H&&(0,r.jsx)(b.D,{className:"flex-1 w-full min-h-0",loadChat:e=>{de(e.title||""),t(e.turns),u(e.id),B(!1),re("idle"),q(!1);const a={useNote:e.useNoteActive??!1,noteContent:e.noteContentUsed||""};G(a),"page"!==a.chatMode&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})},onDeleteAll:pe}),!F&&!W&&H&&(0,r.jsx)(A.z,{triggerOpenCreateModal:oe,onModalOpened:ge}),!F&&!W&&!H&&(0,r.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 relative",children:[(0,r.jsx)(N.B,{isLoading:I,turns:e,settingsMode:F,onReload:()=>{t((e=>{if(e.length<2)return e;const t=e[e.length-1],a=e[e.length-2];return"assistant"===t.role&&"user"===a.role?(s(a.rawContent),e.slice(0,-2)):e})),U(!1),re("idle")},onEditTurn:(e,a)=>{t((t=>{const s=[...t];return s[e]&&(s[e]={...s[e],rawContent:a}),s}))}}),0===e.length&&!V?.chatMode&&(0,r.jsxs)("div",{className:"fixed bottom-20 left-8 flex flex-col gap-2 z-[5]",children:[(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{asChild:!0,children:(0,r.jsx)(m.$,{"aria-label":"Cycle compute level",variant:"ghost",size:"icon",onClick:()=>{const e=V.computeLevel;G({computeLevel:"low"===e?"medium":"medium"===e?"high":"low"})},className:(0,p.cn)("hover:bg-secondary/70","high"===V.computeLevel?"text-red-600":"medium"===V.computeLevel?"text-orange-300":"text-[var(--text)]"),children:(0,r.jsx)(d.cfR,{})})}),(0,r.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)] max-w-80",children:(0,r.jsx)("p",{children:`Compute Level: ${V.computeLevel?.toUpperCase()}. Click to change. [Warning]: beta feature and resource costly.`})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{asChild:!0,children:(0,r.jsx)(m.$,{"aria-label":"Add Web Search Results to LLM Context",variant:"ghost",size:"icon",onClick:()=>{G({chatMode:"web",webMode:V.webMode||L[0].id})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,r.jsx)(c.pqQ,{})})}),(0,r.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,r.jsx)("p",{children:"Add Web Search Results to LLM Context"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{asChild:!0,children:(0,r.jsx)(m.$,{"aria-label":"Add Current Web Page to LLM Context",variant:"ghost",size:"icon",onClick:()=>{G({chatMode:"page"})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,r.jsx)(c.RGv,{})})}),(0,r.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,r.jsx)("p",{children:"Add Current Web Page to LLM Context"})})]})]}),"page"===V?.chatMode&&(0,r.jsx)("div",{className:(0,p.cn)("fixed bottom-16 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-8 z-[2]","transition-all duration-200 ease-in-out",X?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>ee(!0),onMouseLeave:()=>ee(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-6 max-w-full overflow-x-auto px-0",children:[(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(P,{onClick:()=>ue("Provide your summary."),children:"TLDR"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Quick Summary"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(P,{onClick:()=>ue("Extract all key figures, names, locations, and dates mentioned on this page and list them."),children:"Facts"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Numbers, events, names"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(P,{onClick:()=>ue("Find positive developments, achievements, or opportunities mentioned on this page."),children:"Yay!"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Good news"})})]}),(0,r.jsxs)(h.m_,{children:[(0,r.jsx)(h.k$,{children:(0,r.jsx)(P,{onClick:()=>ue("Find concerning issues, risks, or criticisms mentioned on this page."),children:"Oops"})}),(0,r.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,r.jsx)("p",{children:"Bad news"})})]})]})}),"web"===V?.chatMode&&(0,r.jsx)("div",{className:(0,p.cn)("fixed bottom-14 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-10 z-[2]","transition-all duration-200 ease-in-out",te?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>ae(!0),onMouseLeave:()=>ae(!1),children:(0,r.jsx)("div",{className:"flex items-center space-x-4 max-w-full overflow-x-auto px-4 py-1",children:L.map((e=>(0,r.jsx)(R,{onClick:()=>{G({webMode:e.id,chatMode:"web"})},isActive:V.webMode===e.id,title:e.label,children:(0,r.jsx)(e.icon,{size:18})},e.id)))})})]})]}),!F&&!W&&!H&&(0,r.jsx)("div",{className:"p-2 relative z-[10]",children:(0,r.jsx)(j.p,{isLoading:I,message:a,setMessage:s,onSend:()=>ue(a),onStopRequest:me})}),V?.backgroundImage?(0,r.jsx)(v.V,{}):null,(0,r.jsx)(n.l$,{containerStyle:{borderRadius:16,bottom:"60px"},toastOptions:{duration:2e3,position:"bottom-center",style:{background:"var(--bg)",color:"var(--text)",fontSize:"1rem",border:"1px solid var(--text)",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},success:{duration:2e3,style:{background:"var(--bg)",color:"var(--text)",fontSize:"1.25rem"}}}})]})})};s()}catch(D){s(D)}}))},9853:(e,t,a)=>{a.d(t,{S:()=>i});var s=a(6540),r=a(6948),o=a(2951);const n=e=>{const t=e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/"/g,"").replace(/#/g,"").trim();return t&&t.split(/\s+/).slice(0,4).join(" ")||"New Chat"},i=(e,t,a)=>{const[i,l]=(0,s.useState)(""),{config:c}=(0,r.UK)(),d=(0,s.useRef)(null);return(0,s.useEffect)((()=>{if(!e&&t.length>=2&&!i&&c?.generateTitle){d.current&&d.current.abort(),d.current=new AbortController;const e=d.current.signal,a=c?.models?.find((e=>e.id===c.selectedModel));if(!a)return;const s=[...t.slice(0,2).map((e=>({content:e.rawContent||"",role:e.role}))),{role:"user",content:"Create a short 2-4 word title for this chat. Keep it concise, just give me the best one, just one. No explanations or thinking steps needed."}],r=(()=>{const e={body:{model:a.id,messages:s,stream:!["ollama","lmStudio"].includes(a.host||"")},headers:{}};if("ollama"===a.host)return{...e,url:`${c.ollamaUrl}/api/chat`}})();if(!r)return;const i=t=>{e.aborted?console.log("Title generation aborted."):console.error("Title generation failed:",t)};if(["ollama"].includes(a.host||""))fetch(r.url,{method:"POST",headers:{"Content-Type":"application/json",...r.headers},body:JSON.stringify(r.body),signal:e}).then((e=>e.json())).then((e=>{const t=e.choices?.[0]?.message?.content||"",a=n(t);a&&(console.log("Setting chat title (local):",a),l(a))})).catch(i);else{let t="";(0,o.hL)(r.url,r.body,((a,s)=>{if(t=a,e.aborted)console.log("Title streaming aborted during callback.");else if(s){const e=n(t);e&&(console.log("Setting chat title (streaming):",e),l(e))}}),r.headers,a.host||"",e)}}return()=>{d.current&&(d.current.abort(),d.current=null)}}),[e,t,a,c,i]),{chatTitle:i,setChatTitle:l}}}},l={};function c(e){var t=l[e];if(void 0!==t)return t.exports;var a=l[e]={exports:{}};return i[e].call(a.exports,a,a.exports,c),a.exports}c.m=i,e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",a="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",s=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},c.a=(r,o,n)=>{var i;n&&((i=[]).d=-1);var l,c,d,u=new Set,m=r.exports,h=new Promise(((e,t)=>{d=t,c=e}));h[t]=m,h[e]=e=>(i&&e(i),u.forEach(e),h.catch((e=>{}))),r.exports=h,o((r=>{var o;l=(r=>r.map((r=>{if(null!==r&&"object"==typeof r){if(r[e])return r;if(r.then){var o=[];o.d=0,r.then((e=>{n[t]=e,s(o)}),(e=>{n[a]=e,s(o)}));var n={};return n[e]=e=>e(o),n}}var i={};return i[e]=e=>{},i[t]=r,i})))(r);var n=()=>l.map((e=>{if(e[a])throw e[a];return e[t]})),c=new Promise((t=>{(o=()=>t(n)).r=0;var a=e=>e!==i&&!u.has(e)&&(u.add(e),e&&!e.d&&(o.r++,e.push(o)));l.map((t=>t[e](a)))}));return o.r?c:n()}),(e=>(e?d(h[a]=e):c(m),s(i)))),i&&i.d<0&&(i.d=0)},r=[],c.O=(e,t,a,s)=>{if(!t){var o=1/0;for(d=0;d<r.length;d++){for(var[t,a,s]=r[d],n=!0,i=0;i<t.length;i++)(!1&s||o>=s)&&Object.keys(c.O).every((e=>c.O[e](t[i])))?t.splice(i--,1):(n=!1,s<o&&(o=s));if(n){r.splice(d--,1);var l=a();void 0!==l&&(e=l)}}return e}s=s||0;for(var d=r.length;d>0&&r[d-1][2]>s;d--)r[d]=r[d-1];r[d]=[t,a,s]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var a=Object.create(null);c.r(a);var s={};o=o||[null,n({}),n([]),n(n)];for(var r=2&t&&e;"object"==typeof r&&!~o.indexOf(r);r=n(r))Object.getOwnPropertyNames(r).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,c.d(a,s),a},c.d=(e,t)=>{for(var a in t)c.o(t,a)&&!c.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=524,(()=>{var e={524:0};c.O.j=t=>0===e[t];var t=(t,a)=>{var s,r,[o,n,i]=a,l=0;if(o.some((t=>0!==e[t]))){for(s in n)c.o(n,s)&&(c.m[s]=n[s]);if(i)var d=i(c)}for(t&&t(a);l<o.length;l++)r=o[l],c.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return c.O(d)},a=self.webpackChunkchromepanion=self.webpackChunkchromepanion||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})(),c.nc=void 0;var d=c.O(void 0,[465],(()=>c(3003)));d=c.O(d)})();